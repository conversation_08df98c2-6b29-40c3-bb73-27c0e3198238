PODS:
  - AliPlayerSDK_iOS (6.21.0):
    - AliPlayerSDK_iOS/AliPlayerSDK (= 6.21.0)
  - AliPlayerSDK_iOS/AliPlayerSDK (6.21.0)
  - app_badge_plus (1.2.3):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_session (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - captcha_plugin_flutter (0.0.2):
    - Flutter
    - NTESVerifyCode (= 3.6.7)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Auth (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Crashlytics (11.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.15.0)
  - firebase_auth (5.6.1):
    - Firebase/Auth (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_core (3.15.0):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_crashlytics (4.3.8):
    - Firebase/Crashlytics (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_aliplayer (6.21.0):
    - Flutter
    - flutter_aliplayer/AliPlayerSDKFrameworks (= 6.21.0)
    - MJExtension
  - flutter_aliplayer/AliPlayerSDKFrameworks (6.21.0):
    - AliPlayerSDK_iOS (= 6.21.0)
    - Flutter
    - MJExtension
  - flutter_avif_ios (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_image_gallery_saver (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_plugin_engagelab (0.0.3):
    - Flutter
    - MTPush (= 4.4.0)
  - fluttertoast (0.0.2):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - HydraAsync (2.0.6)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libOpenInstallSDK (2.8.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - memory_info (0.0.1):
    - Flutter
  - MJExtension (3.4.2)
  - MTPush (4.4.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NTESVerifyCode (3.6.7)
  - open_file_ios (0.0.1):
    - Flutter
  - openinstall_flutter_plugin (0.0.1):
    - Flutter
    - libOpenInstallSDK
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (101.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.5.6864)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
  - TXIMSDK_Plus_iOS_XCFramework (8.5.6864)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - captcha_plugin_flutter (from `.symlinks/plugins/captcha_plugin_flutter/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_aliplayer (from `.symlinks/plugins/flutter_aliplayer/ios`)
  - flutter_avif_ios (from `.symlinks/plugins/flutter_avif_ios/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_image_gallery_saver (from `.symlinks/plugins/flutter_image_gallery_saver/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_plugin_engagelab (from `.symlinks/plugins/flutter_plugin_engagelab/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - memory_info (from `.symlinks/plugins/memory_info/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - openinstall_flutter_plugin (from `.symlinks/plugins/openinstall_flutter_plugin/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AliPlayerSDK_iOS
    - AppAuth
    - AppCheckCore
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HydraAsync
    - libOpenInstallSDK
    - libwebp
    - Mantle
    - MJExtension
    - MTPush
    - nanopb
    - NTESVerifyCode
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - TXIMSDK_Plus_iOS_XCFramework

EXTERNAL SOURCES:
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  captcha_plugin_flutter:
    :path: ".symlinks/plugins/captcha_plugin_flutter/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_aliplayer:
    :path: ".symlinks/plugins/flutter_aliplayer/ios"
  flutter_avif_ios:
    :path: ".symlinks/plugins/flutter_avif_ios/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_image_gallery_saver:
    :path: ".symlinks/plugins/flutter_image_gallery_saver/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_plugin_engagelab:
    :path: ".symlinks/plugins/flutter_plugin_engagelab/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  memory_info:
    :path: ".symlinks/plugins/memory_info/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  openinstall_flutter_plugin:
    :path: ".symlinks/plugins/openinstall_flutter_plugin/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AliPlayerSDK_iOS: 9598c53c095bb4aca8b956f698f4d9f4ceeadeec
  app_badge_plus: 0e3470f993dd08094e16463f57a7e0db04c6b587
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  captcha_plugin_flutter: 0ceae5dc5e5ee49f83e0c8294c02f5c33f74770f
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_auth: 5be3036c1b9a080697788696db398e637d6c8401
  firebase_core: c727a02c560a53f1f1e56e18f16515eb5753c492
  firebase_crashlytics: 6273355054ed9b46631aa7d5d8a02367ca2c5141
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: a6575e5fbf46b046c58dc211a28a5fbdd8d4c83b
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseCrashlytics: e09d0bc19aa54a51e45b8039c836ef73f32c039a
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: b9a92c1c51bbb81e78fc3142cda6d925d700f8e7
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_aliplayer: 01154455e07cb581b7b716ff48f640ebcc393aaa
  flutter_avif_ios: 2553d2aafda56339cf924e943da89ad3f40af2dd
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_image_gallery_saver: 0453c83412e9691abef94c04c8d180724f5083a8
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_plugin_engagelab: 0808a5c8e94e3aa1bd43b840ff85c4c1c1440710
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libOpenInstallSDK: 4473c8a86c6f2a31147cbcbc63ab5fa9479c78ce
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  memory_info: 0c8ecafff5f646e2957972aee37801131affa512
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MTPush: 88322fc0a046d1bbc9babc7fb7c032d276107e63
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NTESVerifyCode: 3b4dc542427905f7c876f293076012a5ae9735bc
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  openinstall_flutter_plugin: a316342ef16d60a902b5aac724370d4514e092b1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  tencent_cloud_chat_sdk: f60b13e08e8aa0c1efb4e1929e100fdba0f4c7b1
  tencent_cloud_uikit_core: 137e8ae40882b1929508e688182b2818708cc078
  TXIMSDK_Plus_iOS_XCFramework: 0353712b504d2206ce0f4d94b0eb3357673e1cfe
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  volume_controller: 3657a1f65bedb98fa41ff7dc5793537919f31b12
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 755b7033cbfe4180159d79a4296ca5bc1782a943

COCOAPODS: 1.16.2
