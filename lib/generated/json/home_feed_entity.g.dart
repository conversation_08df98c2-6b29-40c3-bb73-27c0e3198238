import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';

HomeFeedEntity $HomeFeedEntityFromJson(Map<String, dynamic> json) {
  final HomeFeedEntity homeFeedEntity = HomeFeedEntity();
  final HomeFeedLuckyGift? luckyGift = jsonConvert.convert<HomeFeedLuckyGift>(json['luckyGift']);
  if (luckyGift != null) {
    homeFeedEntity.luckyGift = luckyGift;
  }
  final List<HomeFeedPopular>? popularList = (json['popular'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HomeFeedPopular>(e) as HomeFeedPopular).toList();
  if (popularList != null) {
    homeFeedEntity.popularList = popularList;
  }
  final List<HomeFeedNotice>? noticeList = (json['notice'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HomeFeedNotice>(e) as HomeFeedNotice).toList();
  if (noticeList != null) {
    homeFeedEntity.noticeList = noticeList;
  }
  final List<HomeFeedMarquee>? marqueeList = (json['marquee'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HomeFeedMarquee>(e) as HomeFeedMarquee).toList();
  if (marqueeList != null) {
    homeFeedEntity.marqueeList = marqueeList;
  }
  final List<HomeFeedWelcome>? welcomeList = (json['welcome'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HomeFeedWelcome>(e) as HomeFeedWelcome).toList();
  if (welcomeList != null) {
    homeFeedEntity.welcomeList = welcomeList;
  }
  return homeFeedEntity;
}

Map<String, dynamic> $HomeFeedEntityToJson(HomeFeedEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['luckyGift'] = entity.luckyGift?.toJson();
  data['popular'] = entity.popularList.map((v) => v.toJson()).toList();
  data['notice'] = entity.noticeList.map((v) => v.toJson()).toList();
  data['marquee'] = entity.marqueeList.map((v) => v.toJson()).toList();
  data['welcome'] = entity.welcomeList.map((v) => v.toJson()).toList();
  return data;
}

extension HomeFeedEntityExtension on HomeFeedEntity {
  HomeFeedEntity copyWith({
    HomeFeedLuckyGift? luckyGift,
    List<HomeFeedPopular>? popularList,
    List<HomeFeedNotice>? noticeList,
    List<HomeFeedMarquee>? marqueeList,
    List<HomeFeedWelcome>? welcomeList,
  }) {
    return HomeFeedEntity()
      ..luckyGift = luckyGift ?? this.luckyGift
      ..popularList = popularList ?? this.popularList
      ..noticeList = noticeList ?? this.noticeList
      ..marqueeList = marqueeList ?? this.marqueeList
      ..welcomeList = welcomeList ?? this.welcomeList;
  }
}

HomeFeedLuckyGift $HomeFeedLuckyGiftFromJson(Map<String, dynamic> json) {
  final HomeFeedLuckyGift homeFeedLuckyGift = HomeFeedLuckyGift();
  final double? receiveMoney = jsonConvert.convert<double>(json['receiveMoney']);
  if (receiveMoney != null) {
    homeFeedLuckyGift.receiveMoney = receiveMoney;
  }
  final double? bonusMoney = jsonConvert.convert<double>(json['bonusMoney']);
  if (bonusMoney != null) {
    homeFeedLuckyGift.bonusMoney = bonusMoney;
  }
  final int? totalReceiveUser = jsonConvert.convert<int>(json['totalReceiveUser']);
  if (totalReceiveUser != null) {
    homeFeedLuckyGift.totalReceiveUser = totalReceiveUser;
  }
  final double? totalReceiveMoney = jsonConvert.convert<double>(json['totalReceiveMoney']);
  if (totalReceiveMoney != null) {
    homeFeedLuckyGift.totalReceiveMoney = totalReceiveMoney;
  }
  final double? remainingAmount = jsonConvert.convert<double>(json['remainingAmount']);
  if (remainingAmount != null) {
    homeFeedLuckyGift.remainingAmount = remainingAmount;
  }
  final double? finishMoney = jsonConvert.convert<double>(json['finishMoney']);
  if (finishMoney != null) {
    homeFeedLuckyGift.finishMoney = finishMoney;
  }
  final int? expiredTime = jsonConvert.convert<int>(json['expiredTime']);
  if (expiredTime != null) {
    homeFeedLuckyGift.expiredTime = expiredTime;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    homeFeedLuckyGift.status = status;
  }
  final List<HomeFeedLuckyGiftReceiveUser>? receiveUserList = (json['receiveUserListVOList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HomeFeedLuckyGiftReceiveUser>(e) as HomeFeedLuckyGiftReceiveUser).toList();
  if (receiveUserList != null) {
    homeFeedLuckyGift.receiveUserList = receiveUserList;
  }
  return homeFeedLuckyGift;
}

Map<String, dynamic> $HomeFeedLuckyGiftToJson(HomeFeedLuckyGift entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['receiveMoney'] = entity.receiveMoney;
  data['bonusMoney'] = entity.bonusMoney;
  data['totalReceiveUser'] = entity.totalReceiveUser;
  data['totalReceiveMoney'] = entity.totalReceiveMoney;
  data['remainingAmount'] = entity.remainingAmount;
  data['finishMoney'] = entity.finishMoney;
  data['expiredTime'] = entity.expiredTime;
  data['status'] = entity.status;
  data['receiveUserListVOList'] = entity.receiveUserList.map((v) => v.toJson()).toList();
  return data;
}

extension HomeFeedLuckyGiftExtension on HomeFeedLuckyGift {
  HomeFeedLuckyGift copyWith({
    double? receiveMoney,
    double? bonusMoney,
    int? totalReceiveUser,
    double? totalReceiveMoney,
    double? remainingAmount,
    double? finishMoney,
    int? expiredTime,
    int? status,
    List<HomeFeedLuckyGiftReceiveUser>? receiveUserList,
  }) {
    return HomeFeedLuckyGift()
      ..receiveMoney = receiveMoney ?? this.receiveMoney
      ..bonusMoney = bonusMoney ?? this.bonusMoney
      ..totalReceiveUser = totalReceiveUser ?? this.totalReceiveUser
      ..totalReceiveMoney = totalReceiveMoney ?? this.totalReceiveMoney
      ..remainingAmount = remainingAmount ?? this.remainingAmount
      ..finishMoney = finishMoney ?? this.finishMoney
      ..expiredTime = expiredTime ?? this.expiredTime
      ..status = status ?? this.status
      ..receiveUserList = receiveUserList ?? this.receiveUserList;
  }
}

HomeFeedLuckyGiftReceiveUser $HomeFeedLuckyGiftReceiveUserFromJson(Map<String, dynamic> json) {
  final HomeFeedLuckyGiftReceiveUser homeFeedLuckyGiftReceiveUser = HomeFeedLuckyGiftReceiveUser();
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    homeFeedLuckyGiftReceiveUser.userNo = userNo;
  }
  final int? bonusMoney = jsonConvert.convert<int>(json['bonusMoney']);
  if (bonusMoney != null) {
    homeFeedLuckyGiftReceiveUser.bonusMoney = bonusMoney;
  }
  final int? finishedTime = jsonConvert.convert<int>(json['finishedTime']);
  if (finishedTime != null) {
    homeFeedLuckyGiftReceiveUser.finishedTime = finishedTime;
  }
  return homeFeedLuckyGiftReceiveUser;
}

Map<String, dynamic> $HomeFeedLuckyGiftReceiveUserToJson(HomeFeedLuckyGiftReceiveUser entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userNo'] = entity.userNo;
  data['bonusMoney'] = entity.bonusMoney;
  data['finishedTime'] = entity.finishedTime;
  return data;
}

extension HomeFeedLuckyGiftReceiveUserExtension on HomeFeedLuckyGiftReceiveUser {
  HomeFeedLuckyGiftReceiveUser copyWith({
    String? userNo,
    int? bonusMoney,
    int? finishedTime,
  }) {
    return HomeFeedLuckyGiftReceiveUser()
      ..userNo = userNo ?? this.userNo
      ..bonusMoney = bonusMoney ?? this.bonusMoney
      ..finishedTime = finishedTime ?? this.finishedTime;
  }
}

HomeFeedPopular $HomeFeedPopularFromJson(Map<String, dynamic> json) {
  final HomeFeedPopular homeFeedPopular = HomeFeedPopular();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeFeedPopular.id = id;
  }
  final String? currencyId = jsonConvert.convert<String>(json['currencyId']);
  if (currencyId != null) {
    homeFeedPopular.currencyId = currencyId;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    homeFeedPopular.noticeType = noticeType;
  }
  final int? noticeClassify = jsonConvert.convert<int>(json['noticeClassify']);
  if (noticeClassify != null) {
    homeFeedPopular.noticeClassify = noticeClassify;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    homeFeedPopular.noticeTitle = noticeTitle;
  }
  final String? noticeContent = jsonConvert.convert<String>(json['noticeContent']);
  if (noticeContent != null) {
    homeFeedPopular.noticeContent = noticeContent;
  }
  final int? beginTime = jsonConvert.convert<int>(json['beginTime']);
  if (beginTime != null) {
    homeFeedPopular.beginTime = beginTime;
  }
  final int? endTime = jsonConvert.convert<int>(json['endTime']);
  if (endTime != null) {
    homeFeedPopular.endTime = endTime;
  }
  final int? receiveType = jsonConvert.convert<int>(json['receiveType']);
  if (receiveType != null) {
    homeFeedPopular.receiveType = receiveType;
  }
  final int? noticeSeq = jsonConvert.convert<int>(json['noticeSeq']);
  if (noticeSeq != null) {
    homeFeedPopular.noticeSeq = noticeSeq;
  }
  final int? noticeStatus = jsonConvert.convert<int>(json['noticeStatus']);
  if (noticeStatus != null) {
    homeFeedPopular.noticeStatus = noticeStatus;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    homeFeedPopular.createTime = createTime;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    homeFeedPopular.intoHistory = intoHistory;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    homeFeedPopular.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    homeFeedPopular.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    homeFeedPopular.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    homeFeedPopular.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(json['platformCode']);
  if (platformCode != null) {
    homeFeedPopular.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    homeFeedPopular.gameId = gameId;
  }
  final String? tempId = jsonConvert.convert<String>(json['tempId']);
  if (tempId != null) {
    homeFeedPopular.tempId = tempId;
  }
  return homeFeedPopular;
}

Map<String, dynamic> $HomeFeedPopularToJson(HomeFeedPopular entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['currencyId'] = entity.currencyId;
  data['noticeType'] = entity.noticeType;
  data['noticeClassify'] = entity.noticeClassify;
  data['noticeTitle'] = entity.noticeTitle;
  data['noticeContent'] = entity.noticeContent;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveType'] = entity.receiveType;
  data['noticeSeq'] = entity.noticeSeq;
  data['noticeStatus'] = entity.noticeStatus;
  data['createTime'] = entity.createTime;
  data['intoHistory'] = entity.intoHistory;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  data['tempId'] = entity.tempId;
  return data;
}

extension HomeFeedPopularExtension on HomeFeedPopular {
  HomeFeedPopular copyWith({
    int? id,
    String? currencyId,
    int? noticeType,
    int? noticeClassify,
    String? noticeTitle,
    String? noticeContent,
    int? beginTime,
    int? endTime,
    int? receiveType,
    int? noticeSeq,
    int? noticeStatus,
    int? createTime,
    int? intoHistory,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
    String? tempId,
  }) {
    return HomeFeedPopular()
      ..id = id ?? this.id
      ..currencyId = currencyId ?? this.currencyId
      ..noticeType = noticeType ?? this.noticeType
      ..noticeClassify = noticeClassify ?? this.noticeClassify
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..noticeContent = noticeContent ?? this.noticeContent
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveType = receiveType ?? this.receiveType
      ..noticeSeq = noticeSeq ?? this.noticeSeq
      ..noticeStatus = noticeStatus ?? this.noticeStatus
      ..createTime = createTime ?? this.createTime
      ..intoHistory = intoHistory ?? this.intoHistory
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId
      ..tempId = tempId ?? this.tempId;
  }
}

HomeFeedNotice $HomeFeedNoticeFromJson(Map<String, dynamic> json) {
  final HomeFeedNotice homeFeedNotice = HomeFeedNotice();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeFeedNotice.id = id;
  }
  final String? currencyId = jsonConvert.convert<String>(json['currencyId']);
  if (currencyId != null) {
    homeFeedNotice.currencyId = currencyId;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    homeFeedNotice.noticeType = noticeType;
  }
  final int? noticeClassify = jsonConvert.convert<int>(json['noticeClassify']);
  if (noticeClassify != null) {
    homeFeedNotice.noticeClassify = noticeClassify;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    homeFeedNotice.noticeTitle = noticeTitle;
  }
  final String? noticeContent = jsonConvert.convert<String>(json['noticeContent']);
  if (noticeContent != null) {
    homeFeedNotice.noticeContent = noticeContent;
  }
  final int? beginTime = jsonConvert.convert<int>(json['beginTime']);
  if (beginTime != null) {
    homeFeedNotice.beginTime = beginTime;
  }
  final int? endTime = jsonConvert.convert<int>(json['endTime']);
  if (endTime != null) {
    homeFeedNotice.endTime = endTime;
  }
  final int? receiveType = jsonConvert.convert<int>(json['receiveType']);
  if (receiveType != null) {
    homeFeedNotice.receiveType = receiveType;
  }
  final int? noticeSeq = jsonConvert.convert<int>(json['noticeSeq']);
  if (noticeSeq != null) {
    homeFeedNotice.noticeSeq = noticeSeq;
  }
  final int? noticeStatus = jsonConvert.convert<int>(json['noticeStatus']);
  if (noticeStatus != null) {
    homeFeedNotice.noticeStatus = noticeStatus;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    homeFeedNotice.createTime = createTime;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    homeFeedNotice.intoHistory = intoHistory;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    homeFeedNotice.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    homeFeedNotice.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    homeFeedNotice.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    homeFeedNotice.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(json['platformCode']);
  if (platformCode != null) {
    homeFeedNotice.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    homeFeedNotice.gameId = gameId;
  }
  final String? tempId = jsonConvert.convert<String>(json['tempId']);
  if (tempId != null) {
    homeFeedNotice.tempId = tempId;
  }
  return homeFeedNotice;
}

Map<String, dynamic> $HomeFeedNoticeToJson(HomeFeedNotice entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['currencyId'] = entity.currencyId;
  data['noticeType'] = entity.noticeType;
  data['noticeClassify'] = entity.noticeClassify;
  data['noticeTitle'] = entity.noticeTitle;
  data['noticeContent'] = entity.noticeContent;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveType'] = entity.receiveType;
  data['noticeSeq'] = entity.noticeSeq;
  data['noticeStatus'] = entity.noticeStatus;
  data['createTime'] = entity.createTime;
  data['intoHistory'] = entity.intoHistory;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  data['tempId'] = entity.tempId;
  return data;
}

extension HomeFeedNoticeExtension on HomeFeedNotice {
  HomeFeedNotice copyWith({
    int? id,
    String? currencyId,
    int? noticeType,
    int? noticeClassify,
    String? noticeTitle,
    String? noticeContent,
    int? beginTime,
    int? endTime,
    int? receiveType,
    int? noticeSeq,
    int? noticeStatus,
    int? createTime,
    int? intoHistory,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
    String? tempId,
  }) {
    return HomeFeedNotice()
      ..id = id ?? this.id
      ..currencyId = currencyId ?? this.currencyId
      ..noticeType = noticeType ?? this.noticeType
      ..noticeClassify = noticeClassify ?? this.noticeClassify
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..noticeContent = noticeContent ?? this.noticeContent
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveType = receiveType ?? this.receiveType
      ..noticeSeq = noticeSeq ?? this.noticeSeq
      ..noticeStatus = noticeStatus ?? this.noticeStatus
      ..createTime = createTime ?? this.createTime
      ..intoHistory = intoHistory ?? this.intoHistory
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId
      ..tempId = tempId ?? this.tempId;
  }
}

HomeFeedMarquee $HomeFeedMarqueeFromJson(Map<String, dynamic> json) {
  final HomeFeedMarquee homeFeedMarquee = HomeFeedMarquee();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeFeedMarquee.id = id;
  }
  final String? currencyId = jsonConvert.convert<String>(json['currencyId']);
  if (currencyId != null) {
    homeFeedMarquee.currencyId = currencyId;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    homeFeedMarquee.noticeType = noticeType;
  }
  final int? noticeClassify = jsonConvert.convert<int>(json['noticeClassify']);
  if (noticeClassify != null) {
    homeFeedMarquee.noticeClassify = noticeClassify;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    homeFeedMarquee.noticeTitle = noticeTitle;
  }
  final String? noticeContent = jsonConvert.convert<String>(json['noticeContent']);
  if (noticeContent != null) {
    homeFeedMarquee.noticeContent = noticeContent;
  }
  final int? beginTime = jsonConvert.convert<int>(json['beginTime']);
  if (beginTime != null) {
    homeFeedMarquee.beginTime = beginTime;
  }
  final int? endTime = jsonConvert.convert<int>(json['endTime']);
  if (endTime != null) {
    homeFeedMarquee.endTime = endTime;
  }
  final int? receiveType = jsonConvert.convert<int>(json['receiveType']);
  if (receiveType != null) {
    homeFeedMarquee.receiveType = receiveType;
  }
  final int? noticeSeq = jsonConvert.convert<int>(json['noticeSeq']);
  if (noticeSeq != null) {
    homeFeedMarquee.noticeSeq = noticeSeq;
  }
  final int? noticeStatus = jsonConvert.convert<int>(json['noticeStatus']);
  if (noticeStatus != null) {
    homeFeedMarquee.noticeStatus = noticeStatus;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    homeFeedMarquee.createTime = createTime;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    homeFeedMarquee.intoHistory = intoHistory;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    homeFeedMarquee.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    homeFeedMarquee.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    homeFeedMarquee.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    homeFeedMarquee.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(json['platformCode']);
  if (platformCode != null) {
    homeFeedMarquee.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    homeFeedMarquee.gameId = gameId;
  }
  final String? tempId = jsonConvert.convert<String>(json['tempId']);
  if (tempId != null) {
    homeFeedMarquee.tempId = tempId;
  }
  return homeFeedMarquee;
}

Map<String, dynamic> $HomeFeedMarqueeToJson(HomeFeedMarquee entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['currencyId'] = entity.currencyId;
  data['noticeType'] = entity.noticeType;
  data['noticeClassify'] = entity.noticeClassify;
  data['noticeTitle'] = entity.noticeTitle;
  data['noticeContent'] = entity.noticeContent;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveType'] = entity.receiveType;
  data['noticeSeq'] = entity.noticeSeq;
  data['noticeStatus'] = entity.noticeStatus;
  data['createTime'] = entity.createTime;
  data['intoHistory'] = entity.intoHistory;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  data['tempId'] = entity.tempId;
  return data;
}

extension HomeFeedMarqueeExtension on HomeFeedMarquee {
  HomeFeedMarquee copyWith({
    int? id,
    String? currencyId,
    int? noticeType,
    int? noticeClassify,
    String? noticeTitle,
    String? noticeContent,
    int? beginTime,
    int? endTime,
    int? receiveType,
    int? noticeSeq,
    int? noticeStatus,
    int? createTime,
    int? intoHistory,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
    String? tempId,
  }) {
    return HomeFeedMarquee()
      ..id = id ?? this.id
      ..currencyId = currencyId ?? this.currencyId
      ..noticeType = noticeType ?? this.noticeType
      ..noticeClassify = noticeClassify ?? this.noticeClassify
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..noticeContent = noticeContent ?? this.noticeContent
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveType = receiveType ?? this.receiveType
      ..noticeSeq = noticeSeq ?? this.noticeSeq
      ..noticeStatus = noticeStatus ?? this.noticeStatus
      ..createTime = createTime ?? this.createTime
      ..intoHistory = intoHistory ?? this.intoHistory
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId
      ..tempId = tempId ?? this.tempId;
  }
}

HomeFeedWelcome $HomeFeedWelcomeFromJson(Map<String, dynamic> json) {
  final HomeFeedWelcome homeFeedWelcome = HomeFeedWelcome();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeFeedWelcome.id = id;
  }
  final String? currencyId = jsonConvert.convert<String>(json['currencyId']);
  if (currencyId != null) {
    homeFeedWelcome.currencyId = currencyId;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    homeFeedWelcome.noticeType = noticeType;
  }
  final int? noticeClassify = jsonConvert.convert<int>(json['noticeClassify']);
  if (noticeClassify != null) {
    homeFeedWelcome.noticeClassify = noticeClassify;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    homeFeedWelcome.noticeTitle = noticeTitle;
  }
  final String? noticeContent = jsonConvert.convert<String>(json['noticeContent']);
  if (noticeContent != null) {
    homeFeedWelcome.noticeContent = noticeContent;
  }
  final int? beginTime = jsonConvert.convert<int>(json['beginTime']);
  if (beginTime != null) {
    homeFeedWelcome.beginTime = beginTime;
  }
  final int? endTime = jsonConvert.convert<int>(json['endTime']);
  if (endTime != null) {
    homeFeedWelcome.endTime = endTime;
  }
  final int? receiveType = jsonConvert.convert<int>(json['receiveType']);
  if (receiveType != null) {
    homeFeedWelcome.receiveType = receiveType;
  }
  final int? noticeSeq = jsonConvert.convert<int>(json['noticeSeq']);
  if (noticeSeq != null) {
    homeFeedWelcome.noticeSeq = noticeSeq;
  }
  final int? noticeStatus = jsonConvert.convert<int>(json['noticeStatus']);
  if (noticeStatus != null) {
    homeFeedWelcome.noticeStatus = noticeStatus;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    homeFeedWelcome.createTime = createTime;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    homeFeedWelcome.intoHistory = intoHistory;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    homeFeedWelcome.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    homeFeedWelcome.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    homeFeedWelcome.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    homeFeedWelcome.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(json['platformCode']);
  if (platformCode != null) {
    homeFeedWelcome.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    homeFeedWelcome.gameId = gameId;
  }
  final String? tempId = jsonConvert.convert<String>(json['tempId']);
  if (tempId != null) {
    homeFeedWelcome.tempId = tempId;
  }
  return homeFeedWelcome;
}

Map<String, dynamic> $HomeFeedWelcomeToJson(HomeFeedWelcome entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['currencyId'] = entity.currencyId;
  data['noticeType'] = entity.noticeType;
  data['noticeClassify'] = entity.noticeClassify;
  data['noticeTitle'] = entity.noticeTitle;
  data['noticeContent'] = entity.noticeContent;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveType'] = entity.receiveType;
  data['noticeSeq'] = entity.noticeSeq;
  data['noticeStatus'] = entity.noticeStatus;
  data['createTime'] = entity.createTime;
  data['intoHistory'] = entity.intoHistory;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  data['tempId'] = entity.tempId;
  return data;
}

extension HomeFeedWelcomeExtension on HomeFeedWelcome {
  HomeFeedWelcome copyWith({
    int? id,
    String? currencyId,
    int? noticeType,
    int? noticeClassify,
    String? noticeTitle,
    String? noticeContent,
    int? beginTime,
    int? endTime,
    int? receiveType,
    int? noticeSeq,
    int? noticeStatus,
    int? createTime,
    int? intoHistory,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
    String? tempId,
  }) {
    return HomeFeedWelcome()
      ..id = id ?? this.id
      ..currencyId = currencyId ?? this.currencyId
      ..noticeType = noticeType ?? this.noticeType
      ..noticeClassify = noticeClassify ?? this.noticeClassify
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..noticeContent = noticeContent ?? this.noticeContent
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveType = receiveType ?? this.receiveType
      ..noticeSeq = noticeSeq ?? this.noticeSeq
      ..noticeStatus = noticeStatus ?? this.noticeStatus
      ..createTime = createTime ?? this.createTime
      ..intoHistory = intoHistory ?? this.intoHistory
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId
      ..tempId = tempId ?? this.tempId;
  }
}