import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/country_entity.dart';

CountryEntity $CountryEntityFromJson(Map<String, dynamic> json) {
  final CountryEntity countryEntity = CountryEntity();
  final List<CountryList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CountryList>(e) as CountryList).toList();
  if (list != null) {
    countryEntity.list = list;
  }
  return countryEntity;
}

Map<String, dynamic> $CountryEntityToJson(CountryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CountryEntityExtension on CountryEntity {
  CountryEntity copyWith({
    List<CountryList>? list,
  }) {
    return CountryEntity()
      ..list = list ?? this.list;
  }
}

CountryList $CountryListFromJson(Map<String, dynamic> json) {
  final CountryList countryList = CountryList();
  final String? areaName = jsonConvert.convert<String>(json['areaName']);
  if (areaName != null) {
    countryList.areaName = areaName;
  }
  final String? areaCode = jsonConvert.convert<String>(json['areaCode']);
  if (areaCode != null) {
    countryList.areaCode = areaCode;
  }
  final String? areaIcon = jsonConvert.convert<String>(json['areaIcon']);
  if (areaIcon != null) {
    countryList.areaIcon = areaIcon;
  }
  return countryList;
}

Map<String, dynamic> $CountryListToJson(CountryList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['areaName'] = entity.areaName;
  data['areaCode'] = entity.areaCode;
  data['areaIcon'] = entity.areaIcon;
  return data;
}

extension CountryListExtension on CountryList {
  CountryList copyWith({
    String? areaName,
    String? areaCode,
    String? areaIcon,
  }) {
    return CountryList()
      ..areaName = areaName ?? this.areaName
      ..areaCode = areaCode ?? this.areaCode
      ..areaIcon = areaIcon ?? this.areaIcon;
  }
}