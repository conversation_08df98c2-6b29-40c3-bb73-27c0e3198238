import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../../../core/base/base_stateful_page.dart';
import '../../../../../../core/constants/assets.dart';
import '../../../../../../core/utils/string_util.dart';
import '../../../../../../shared/widgets/common_button.dart';
import '../../../../../../shared/widgets/common_switch.dart';
import '../../../../../../shared/widgets/common_textformfield.dart';
import 'add_wallet_cubit.dart';
import 'add_wallet_state.dart';

class AddWalletPage extends BasePage {
  const AddWalletPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddWalletPageState();
}

class _AddWalletPageState extends BasePageState<AddWalletPage> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController();

  @override
  void initState() {
    pageTitle = "add_wallet_account".tr();
    context.read<AddWalletCubit>().fetchBankListData();

    super.initState();
  }

  @override
  void dispose() {
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: LayoutBuilder(builder: (context, constraints) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.5.gw),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(minHeight: constraints.maxHeight),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    Column(
                      children: [
                        SizedBox(height: 24.gw),
                        _buildFormFieldsSection(),
                        SizedBox(height: 10.gw),
                        _buildDefaultCardSwitchSection(),
                      ],
                    ),
                    const Spacer(),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 60.gw,
                        top: 20.gw,
                      ),
                      child: _buildSubmitButton(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildFormFieldsSection() {
    return CommonCard(
      radius: 12.gw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AneText("wallet_address".tr(), style: context.textTheme.secondary.fs20.w500),
          SizedBox(height: 32.gw),
          CommonTextFormField(
            controller: textControllerBankNum,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "wallet_address_cannot_be_empty".tr();
              }
              return null;
            },
            hintText: "wallet_account_hint".tr(),
            prefixIcon: _prefixIcon(Assets.iconWithdrawAddress),
          ),
          SizedBox(height: 32.gw),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => context
                .read<AddWalletCubit>()
                .onClickShowBankListPicker(context: context, controller: textControllerBankName),
            child: CommonTextFormField(
              controller: textControllerBankName,
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return "wallet_name_hint".tr();
                }
                return null;
              },
              hintText: "wallet_name_hint".tr(),
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: 16.gw),
                child: SvgPicture.asset(
                  Assets.iconToolBarArrowDown,
                  width: 12.gw,
                  height: 12.gw,
                ),
              ),
              inputEnable: false,
              prefixIcon: _prefixIcon(Assets.iconWithdrawProtocol),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AneText("set_as_default_wallet".tr(), style: context.textTheme.title.fs16.w500),
          BlocBuilder<AddWalletCubit, AddWalletState>(
            builder: (context, state) {
              return CommonSwitch(
                value: state.isDefaultCard,
                onChanged: (value) => context.read<AddWalletCubit>().onClickSwitch(value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
        title: 'submit'.tr(),
        onPressed: () {
          sl<NavigatorService>().unFocus();
          if (formKey.currentState?.validate() ?? false) {
            context.read<AddWalletCubit>().onSubmit(
                  context,
                  cardNo: textControllerBankNum.text,
                );
          }
        });
  }

  Widget _prefixIcon(String iconName) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: Image.asset(iconName, width: 20.gw, height: 20.gw),
    );
  }
}
