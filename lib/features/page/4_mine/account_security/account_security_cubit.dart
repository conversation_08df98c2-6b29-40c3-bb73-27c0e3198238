
import 'package:bloc/bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'account_security_state.dart';

class AccountSecurityCubit extends Cubit<AccountSecurityState> {
  AccountSecurityCubit() : super(AccountSecurityState().init());

  void updateField(UserFieldType field, String value) {
    try {
      switch (field) {
        case UserFieldType.nickName:
          emit(state.copyWith(nickName: value));
          break;
        case UserFieldType.account:
          emit(state.copyWith(account: value));
          break;
        case UserFieldType.phone:
          emit(state.copyWith(phone: value));
          break;
        case UserFieldType.email:
          emit(state.copyWith(email: value));
          break;
        default:
          throw ArgumentError('Unknown field: $field');
      }
    } catch (e) {
      LogE('Error updating field: $e');
    }
  }

  updateMovieTabVisible(bool isVisible) async {
    GSEasyLoading.showLoading();
    final flag = await UserApi.updateMovieTabVisible(isVisible);
    GSEasyLoading.dismiss();
    if (flag) {
      final userInfo = sl<UserCubit>().state.userInfo;
      final entity = userInfo?.copyWith(tiktokTabVisible:isVisible);
      sl<UserCubit>().setUserInfo(entity);
    }
  }

}
