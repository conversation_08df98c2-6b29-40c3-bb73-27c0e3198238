import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/statement.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/statement/statement_cell.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'statement_state.dart';

class StatementCubit extends Cubit<StatementState> {
  StatementCubit() : super(const StatementState()) {
    fetchListFilterWayList();
    fetchListFilterTypeList();
    fetchListData();
  }

  String lastWayCode = "";
  String lastTypeCode = "";
  void currentDateTypeChanged(RecordDateType type) {
    if (state.currentDateType != type) {
      emit(state.copyWith(
        currentDateType: type,
        pageNo: 1,
        isNoMoreDataState: false,
        // 是否同时清空列表并进入 loading，可按需放开：
        // dataList: const [],
        // netState: NetState.loading,
      ));
      fetchListData();
    }
  }

  void updatePageNo(int pageNo) {
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void updateIsNoMoreDataState(bool value) {
    emit(state.copyWith(isNoMoreDataState: value));
  }

  Future<void> fetchListFilterWayList() async {
    final list = await StatementApi.fetchStatementFilterWay();
    final tmpList = list
        .map((e) => TransactFilterItem.fromStatementFilterWay(e))
        .toList(growable: false);

    if (tmpList.isNotEmpty) {
      final allType = TransactFilterItem(name: "all".tr(), code: "", isSel: true);
      final merged = <TransactFilterItem>[allType, ...tmpList];
      emit(state.copyWith(filterWayList: merged));
    }
  }

  Future<void> fetchListFilterTypeList() async {
    final list = await StatementApi.fetchStatementFilterType();
    final tmpList = list
        .map((e) => TransactFilterItem.fromStatementFilterType(e))
        .toList(growable: false);

    if (tmpList.isNotEmpty) {
      final allType = TransactFilterItem(name: "all".tr(), code: "", isSel: true);
      final merged = <TransactFilterItem>[allType, ...tmpList];
      emit(state.copyWith(filterTypeList: merged));
    }
  }

  void fetchListDataWithFilter() {
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).join(",");

    if (wayCode != lastWayCode || typeCode != lastTypeCode) {
      lastWayCode = wayCode;
      lastTypeCode = typeCode;
      fetchListData();
    }
  }

  Future<void> fetchListData() async {
    // 可选：先发一帧 loading
    emit(state.copyWith(netState: NetState.loading));

    GSEasyLoading.showLoading();

    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).join(",");

    final result = await StatementApi.fetchStatement(
      pageNo: state.pageNo,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      wayCode: wayCode,
      typeCode: typeCode,
    );

    GSEasyLoading.dismiss();

    if (result == null) {
      emit(state.copyWith(
        netState: NetState.failed,
        dataList: const [],
        isNoMoreDataState: false,
      ));
      return;
    }

    final pageModels = result.records
        .map((e) => GSStateListCellModel.formStatementRecords(e))
        .toList(growable: false);

    final merged = state.pageNo == 1
        ? pageModels
        : [...?state.dataList, ...pageModels];

    final noMore = result.total <= merged.length;

    emit(state.copyWith(
      dataList: merged,
      isNoMoreDataState: noMore,
      netState: merged.isEmpty ? NetState.empty : NetState.success,
    ));
  }

}
