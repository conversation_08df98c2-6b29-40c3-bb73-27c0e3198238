import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// A reusable section widget for profile pages
/// Creates a card-like container with a header and content
class ProfileSection extends StatelessWidget {
  final String title;
  final Widget child;

  const ProfileSection({
    super.key,
    required this.title,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.gw),
      decoration: BoxDecoration(
        color: context.theme.dividerColor,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            height: 49.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.borderA,
              borderRadius: BorderRadius.circular(8.gw),
            ),
            child: Row(
              children: [
                AneText(
                  title,
                  style: context.textTheme.secondary.w500.fs18,
                ),
              ],
            ),
          ),
          // Section Content
          child,
        ],
      ),
    );
  }
}
