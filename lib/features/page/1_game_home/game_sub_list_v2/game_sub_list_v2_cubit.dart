import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/game_recently_util.dart';
import 'package:wd/core/utils/game_util.dart';

import 'game_sub_list_v2_state.dart';

class GameSubListV2Cubit extends Cubit<GameSubListV2State> {
  GameSubListV2Cubit(GamePlatformV2 platform) : super(GameSubListV2State(platform: platform)) {
    fetchData();
  }

  fetchData() async {
    emit(state.copyWith(dataNetState: NetState.loading));
    try {
      final res = await ChannelApi.fetchPlatformGameListV2(
        gameClassCode: state.platform.gameClassCode,
        platformId: state.platform.name == 'all_game'.tr() ? null :  state.platform.id, // 全部不用传platformId
      );
      emit(state.copyWith(dataNetState: NetState.success));
      emit(state.copyWith(dataList: res));
    } catch (e) {
      emit(state.copyWith(dataNetState: NetState.failed));
    }
  }

  bool isFetchingGameLogin = false;

  onChangeSelectedPlatform(GamePlatformV2 platform) {
    emit(state.copyWith(platform: platform));
    fetchData();
  }

  onChangeIsExpanded(bool isExpanded) {
    emit(state.copyWith(isExpanded: isExpanded));
  }

  onChangeKeyword(String keyword) {
    emit(state.copyWith(keyword: keyword));
  }

  /// 根据数据类型判断前往不同界面，自营彩票/webView
  onClickGameCell(GameV2 game) {
    AuthUtil.checkIfLogin(() {
      GameUtil().onClickGameCell(game: game);
    });
  }

  onClickGameFav({
    required bool isFav,
    required GameV2 game,
  }) async {
    final flag = await GameUtil().onClickGameFav(isFav: isFav, game: game);
    // 触发状态更新
    if (flag) {
      // 创建新的游戏列表
      final games = state.dataList.map((g) {
        if (g.id == game.id) {
          return g.copyWith(isSavour: !game.isSavour);
        }
        return g;
      }).toList();
      emit(state.copyWith(dataList: games));
    }
  }
}
