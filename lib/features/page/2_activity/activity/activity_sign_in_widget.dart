import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/2_activity/activity/check_in_item_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class ActivitySignInWidget extends StatefulWidget {
  final DailyCheckInEntity model;
  final NetState netState;
  final double totalGoldEarned; // 累计奖励
  final void Function(DailyCheckInItem model) onClickCheckIn;
  final GestureTapCallback onClickMoreList;
  final GestureTapCallback onClickGoRecordsList;

  const ActivitySignInWidget({
    super.key,
    required this.model,
    required this.netState,
    required this.totalGoldEarned,
    required this.onClickCheckIn,
    required this.onClickMoreList,
    required this.onClickGoRecordsList,
  });

  @override
  State<ActivitySignInWidget> createState() => _ActivitySignInWidgetState();
}

class _ActivitySignInWidgetState extends State<ActivitySignInWidget> {
  Widget _buildCustomPadding(List<Widget> content) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.gw),
      child: Column(children: [
        SizedBox(
          height: MediaQuery.of(context).padding.top,
        ),
        Container(
          padding: EdgeInsets.all(12.gw),
          height: 229.gw,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
          ),
          child: Column(
            children: content,
          ),
        ),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildCustomPadding([
      /// 菊花
      if (widget.netState == NetState.errorShowRefresh) Container(),
      if (widget.netState == NetState.dataSuccessState &&
          widget.model.shortList.isEmpty)
        SizedBox(
          height: 205.gw,
          child: Center(
            child: SizedBox(
              width: 15.gw,
              height: 15.gw,
              child: CircularProgressIndicator(
                strokeWidth: 1.gw,
              ),
            ),
          ),
        ),

      ///
      ///
      if (widget.netState == NetState.dataSuccessState &&
          widget.model.shortList.isNotEmpty)
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSignInDaysTitle(),
            _buildRecordBtn(),
          ],
        ),
      if (widget.netState == NetState.dataSuccessState &&
          widget.model.shortList.isNotEmpty)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 34.gw),
            _buildSignInDays(),
            SizedBox(height: 16.gw),
            _buildSignInButton(),
          ],
        ),
    ]);
  }

  Widget _buildSignInDaysTitle() {
    return InkWell(
      onTap: widget.onClickMoreList,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'act_daily_check_in'.tr(),
            style: TextStyle(
              fontSize: 16.fs,
              fontWeight: FontWeight.w400,
              color: const Color(0xffffffff),
            ),
          ),
          SizedBox(width: 8.gw),
          Icon(
            Icons.keyboard_arrow_down_sharp,
            size: 20.gw,
            color: const Color(0xffffffff),
          ),
        ],
      ),
    );
  }

  _buildRecordBtn() {
    return InkWell(
      onTap: widget.onClickGoRecordsList,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 6.gw),
        height: 32.gw,
        decoration: ShapeDecoration(
          color: const Color(0xFF6E5502),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 1,
              color: Color(0xFFC09404),
            ),
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        child: AneText(
          'act_reward_history'.tr(),
          textAlign: TextAlign.right,
          style: context.textTheme.primary.fs12,
        ),
      ),
    );
  }

  Widget _buildSignInDays() {
    return SizedBox(
      height: 75.gw,
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        clipBehavior: Clip.none,
        itemBuilder: (context, index) {
          final model = widget.model.shortList[index];
          return CheckInItemWidget(
            model: model,
            onClickCheckIn: widget.onClickCheckIn,
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(width: 8.gw);
        },
        itemCount: widget.model.shortList.length,
      ),
    );
  }

  Widget _buildSignInButton() {
    return SizedBox(
      height: 42.gw,
      width: double.infinity,
      child: Row(children: [
        Expanded(
          flex: 1,
          child: RotatedLineBtn(
            height: 42.gw,
            content: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                AneText(
                  'act_check_in_streak'.tr(),
                  style: context.textTheme.title,
                ),
                AneText(
                  "${widget.model.totalDays} ${'act_day'.tr()}",
                  style: context.textTheme.primary.fs16.w500,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          flex: 1,
          child: RotatedLineBtn(
            height: 45,
            content: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                AneText(
                  'act_total_rewards'.tr(),
                  style: context.textTheme.title,
                ),
                AneText(
                  "${widget.totalGoldEarned.formattedMoney} ${'act_coin'.tr()}",
                  style: context.textTheme.primary.fs16.w500,
                ),
              ],
            ),
          ),
        ),
      ]),
    );
  }
}

class RotatedLineBtn extends StatelessWidget {
  final double height;
  final Widget content;
  final GestureTapCallback? onTap;
  const RotatedLineBtn({
    super.key,
    required this.height,
    this.onTap,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: height,
            child: ClipPath(
              clipper: _TopHalfClipper(),
              child: Container(
                height: height,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(
                      width: 0.7,
                      color: Color(0xFF2E2E2E),
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ),
          Container(
            height: height,
            padding: const EdgeInsets.all(4),
            child: Container(
              decoration: ShapeDecoration(
                color: const Color(0xFF161616),
                shape: RoundedRectangleBorder(
                  side: const BorderSide(
                    width: 0.50,
                    color: Color(0xFF2E2E2E),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          Container(
            height: height,
            padding: const EdgeInsets.all(4),
            child: Container(
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: Image.asset(
                      "assets/images/activity/line_shadow.png",
                      height: 10,
                      fit: BoxFit.fitHeight,
                    ),
                  ),
                ],
              ),
            ),
          ),
          content
        ],
      ),
    );
  }
}

class _TopHalfClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    return Path()
      ..addRect(Rect.fromPoints(
          Offset(0, size.height * 0.4), Offset(size.width, size.height)));
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}
