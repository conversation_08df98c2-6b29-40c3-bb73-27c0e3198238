import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/shared/widgets/common_progress_bar.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/core/theme/themes.dart';

class ActivityTaskProcessCell extends StatelessWidget {
  final ActivityTask model;
  final VoidCallback onClickDoTask;

  /// 点击完成任务
  const ActivityTaskProcessCell({
    super.key,
    required this.model,
    required this.onClickDoTask,
  });

  @override
  Widget build(BuildContext context) {
    Widget body = Container();

    body = buildInProcessContent(model, context);

    return Container(
      // clipBehavior: Clip.hardEdge,
      // decoration: ShapeDecoration(
      //   color: const Color(0xFF101010),
      //   shape: RoundedRectangleBorder(
      //     borderRadius: BorderRadius.circular(16),
      //   ),
      // ),
      child: body,
    );
  }

  buildInProcessContent(ActivityTask task, BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(
            category: _getCategoryNameBy(task.subReceiveType),
            amount: (task.sumAmount - task.finishAmount).formattedMoney,
          ),
          SizedBox(height: 24.gw),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            AneText(
              'act_task_progress'.tr(),
              style: context.textTheme.title.fs16,
            ),
            Text(
                "${task.finishAmount.formattedMoney}/${task.sumAmount.formattedMoney}"),
          ]),
          SizedBox(height: 12.gw),
          CommonProgressBar(
              total: task.sumAmount.toInt(), progress: task.finishAmount/task.sumAmount, itemWidth: 21.32.gw,),
          SizedBox(height: 24.gw),
          InkWell(
            onTap: onClickDoTask,
            child: Container(
              height: 25.gw,
              width: double.infinity,
              decoration: ShapeDecoration(
                color: context.colorTheme.btnBgPrimary,
                shape: RoundedRectangleBorder(
                  side: const BorderSide(
                    width: 1,
                    color: Color(0xFFFFE157),
                  ),
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: Center(
                child: Text(
                  'act_task_go_to_doing'.tr(),
                  style: TextStyle(
                    color: const Color(0xFF030303),
                    fontSize: 12.fs,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(double progressFactor) {
    return Container(
      width: double.infinity,
      height: 8.gw,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progressFactor,
        heightFactor: 1,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Color(0xFFAA8F6F),
                Color(0xFFFFECD0),
              ],
            ),
            borderRadius: BorderRadius.circular(2.gw),
          ),
        ),
      ),
    );
  }

  _buildTitle({String? category, String? amount}) {
    return Text(
      "完成下个$category投注任务还需要$amount投注额",
      style: TextStyle(
        color: Colors.white,
        fontSize: 16.fs,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  _getCategoryNameBy(int type) {
    // 10-视讯 11-捕鱼 12-棋牌 13-电子 14-彩票 15-体育
    var name = "当前";
    switch (type) {
      case 10:
        name = "视讯";
        break;
      case 11:
        name = "捕鱼";
        break;
      case 12:
        name = "棋牌";
        break;
      case 13:
        name = "电子";
        break;
      case 14:
        name = "彩票";
        break;
      case 15:
        name = "体育";
        break;
    }
    return name;
  }
}
