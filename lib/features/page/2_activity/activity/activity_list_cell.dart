import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/utils/image_cache_manager.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

class ActivityListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  final ActivityRecords model;

  const ActivityListCell({
    super.key,
    required this.model,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 252 / 98,
      child: Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.gw),
        ),
        margin: const EdgeInsets.only(bottom: 5),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Expanded(
              child: AppImage(
            imageUrl: model.bannerImage,
            cacheManager: ImageCacheManager(),
            placeholder: Container(
              color: Colors.white,
            ),
            fit: BoxFit.cover,
            width: double.infinity,
          )),
        ]),
      ),
    );
  }
}
