import 'package:flutter/widgets.dart';

class GradientBoxBorder extends BoxBorder {
  const GradientBoxBorder({required this.gradient, this.width = 1.0});

  final Gradient gradient;

  final double width;

  @override
  BorderSide get bottom => BorderSide.none;

  @override
  BorderSide get top => BorderSide.none;

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.only(top: width, bottom: width);

  @override
  bool get isUniform => true;

  @override
  void paint(
    Canvas canvas,
    Rect rect, {
    TextDirection? textDirection,
    BoxShape shape = BoxShape.rectangle,
    BorderRadius? borderRadius,
  }) {
    switch (shape) {
      case BoxShape.circle:
        assert(
          borderRadius == null,
          'A borderRadius can only be given for rectangular boxes.',
        );
        _paintCircle(canvas, rect);
        break;
      case BoxShape.rectangle:
        if (borderRadius != null) {
          _paintRRect(canvas, rect, borderRadius);
          return;
        }
        _paintRect(canvas, rect);
        break;
    }
  }

  void _paintRect(Canvas canvas, Rect rect) {
    final paint = _getPaint(rect);

    // Draw top border
    canvas.drawLine(
      Offset(rect.left, rect.top + width / 2),
      Offset(rect.right, rect.top + width / 2),
      paint,
    );

    // Draw bottom border
    canvas.drawLine(
      Offset(rect.left, rect.bottom - width / 2),
      Offset(rect.right, rect.bottom - width / 2),
      paint,
    );
  }

  void _paintRRect(Canvas canvas, Rect rect, BorderRadius borderRadius) {
    final paint = _getPaint(rect);

    // Draw top border
    canvas.drawLine(
      Offset(rect.left, rect.top + width / 2),
      Offset(rect.right, rect.top + width / 2),
      paint,
    );

    // Draw bottom border
    canvas.drawLine(
      Offset(rect.left, rect.bottom - width / 2),
      Offset(rect.right, rect.bottom - width / 2),
      paint,
    );
  }

  void _paintCircle(Canvas canvas, Rect rect) {
    final paint = _getPaint(rect);

    // For circles, draw top and bottom arcs
    final center = rect.center;
    final radius = rect.shortestSide / 2.0;

    // Draw top arc (from 180° to 0°)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius - width / 2),
      -3.14159, // -π (180°)
      3.14159,  // π (180°)
      false,
      paint,
    );

    // Draw bottom arc (from 0° to 180°)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius - width / 2),
      0,        // 0°
      3.14159,  // π (180°)
      false,
      paint,
    );
  }

  @override
  ShapeBorder scale(double t) {
    return this;
  }

  Paint _getPaint(Rect rect) {
    return Paint()
      ..strokeWidth = width
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke;
  }
}