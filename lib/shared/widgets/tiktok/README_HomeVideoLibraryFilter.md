# HomeVideoLibraryFilterView 使用指南

## 概述

`HomeVideoLibraryFilterView` 现在使用专属的 `HomeVideoLibraryFilterItem` 模型，不再直接依赖 `VideoFilterEntity`。这使得组件更加灵活，可以接受各种类型的数据源。

## 核心模型

### HomeVideoLibraryFilterItem

```dart
class HomeVideoLibraryFilterItem {
  final String id;           // 唯一标识符
  final String title;        // 显示标题
  final String? subtitle;    // 副标题（可选）
  final dynamic originalData; // 原始数据，方便后续使用
}
```

## 转换方法

### 1. 从 VideoFilterEntity 转换

```dart
// 单个转换
final item = HomeVideoLibraryFilterItem.fromVideoFilterEntity(videoFilterEntity);

// 列表转换
final items = videoFilterEntities.toHomeVideoLibraryFilterItems();

// 列表转换并添加"全部"选项
final items = videoFilterEntities.toHomeVideoLibraryFilterItemsWithAll();
```

### 2. 从 Map 转换

```dart
// 单个转换
final item = HomeVideoLibraryFilterItem.fromMap({
  'id': '1',
  'name': '热门',
  'count': 100
}, idKey: 'id', titleKey: 'name');

// 列表转换
final items = mapList.toHomeVideoLibraryFilterItems(
  idKey: 'id',
  titleKey: 'name',
);

// 列表转换并添加"全部"选项
final items = mapList.toHomeVideoLibraryFilterItemsWithAll(
  idKey: 'id',
  titleKey: 'name',
  allTitle: '全部内容',
);
```

### 3. 从自定义对象转换

```dart
class CustomCategory {
  final String code;
  final String displayName;
  final int sortOrder;
  
  CustomCategory({
    required this.code,
    required this.displayName,
    required this.sortOrder,
  });
}

// 列表转换
final items = HomeVideoLibraryFilterItemExtensions.fromObjectList(
  customCategories,
  getId: (category) => category.code,
  getTitle: (category) => category.displayName,
);
```

### 4. 创建"全部"选项

```dart
final allItem = HomeVideoLibraryFilterItem.all(title: '全部');
```

## 使用示例

### 基本使用

```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late List<HomeVideoLibraryFilterItem> filterItems;
  late HomeVideoLibraryFilterItem currentItem;

  @override
  void initState() {
    super.initState();
    _initializeFilterItems();
  }

  void _initializeFilterItems() {
    // 从 VideoFilterEntity 转换
    final videoFilterEntities = [
      VideoFilterEntity()..videoCategoryId = '1'..videoCategory = '游戏',
      VideoFilterEntity()..videoCategoryId = '2'..videoCategory = '音乐',
      VideoFilterEntity()..videoCategoryId = '3'..videoCategory = '舞蹈',
    ];
    
    filterItems = videoFilterEntities.toHomeVideoLibraryFilterItemsWithAll();
    currentItem = filterItems.first;
  }

  void _onFilterSelected(HomeVideoLibraryFilterItem item) {
    setState(() {
      currentItem = item;
    });
    
    // 可以访问原始数据
    if (item.originalData is VideoFilterEntity) {
      final originalEntity = item.originalData as VideoFilterEntity;
      print('选中的原始数据: ${originalEntity.videoCategoryId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return HomeVideoLibraryFilterView(
      dataList: filterItems,
      current: currentItem,
      onFilterSelected: _onFilterSelected,
    );
  }
}
```

### 从 API 数据转换

```dart
// 假设从 API 获取的数据
final apiData = [
  {'category_id': '1', 'category_name': '热门', 'video_count': 100},
  {'category_id': '2', 'category_name': '最新', 'video_count': 50},
  {'category_id': '3', 'category_name': '推荐', 'video_count': 75},
];

// 转换为过滤器项
final filterItems = apiData.toHomeVideoLibraryFilterItemsWithAll(
  idKey: 'category_id',
  titleKey: 'category_name',
  allTitle: '全部视频',
);
```

## 优势

1. **灵活性**: 不再绑定特定的实体类型，可以接受任何数据源
2. **可扩展性**: 通过 `originalData` 字段保留原始数据，方便后续使用
3. **类型安全**: 提供多种转换方法，确保类型安全
4. **便捷性**: 提供扩展方法，简化转换过程
5. **一致性**: 统一的数据模型，便于维护和扩展

## 迁移指南

### 从旧版本迁移

如果你之前使用的是 `VideoFilterEntity`，只需要简单的转换：

```dart
// 旧代码
HomeVideoLibraryFilterView(
  dataList: videoFilterEntities,
  current: currentEntity,
  onFilterSelected: (entity) { ... },
)

// 新代码
HomeVideoLibraryFilterView(
  dataList: videoFilterEntities.toHomeVideoLibraryFilterItemsWithAll(),
  current: HomeVideoLibraryFilterItem.fromVideoFilterEntity(currentEntity),
  onFilterSelected: (item) { 
    // 可以通过 item.originalData 访问原始 VideoFilterEntity
    final entity = item.originalData as VideoFilterEntity;
    // 处理逻辑...
  },
)
```

## 注意事项

1. `id` 字段用于唯一标识，确保不重复
2. `originalData` 字段保存原始数据，类型为 `dynamic`，使用时需要类型检查
3. 转换方法都提供了默认参数，可以根据实际数据格式调整
4. 建议使用扩展方法进行转换，代码更简洁
