import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// 指标卡片 图标+文字+指标（底部带虚线）
/// Metric card with icon, text and value (with dotted line at bottom)
class MetricCard extends StatelessWidget {
  const MetricCard(
    this.title, {
    super.key,
    required this.iconPath,
    this.iconSize,
    required this.value,
    this.onTap,
    this.suffixIcon,
  });

  /// 卡片标题 / Card title
  final String title;
  
  /// 左侧图标路径 / Left icon path
  final String iconPath;
  
  /// 图标尺寸，可选，默认使用 34x29 / Icon size, optional, default 34x29
  final Size? iconSize;
  
  /// 指标数值 / Metric value
  final String value;
  
  /// 卡片点击回调 / Card tap callback
  final VoidCallback? onTap;
  
  /// 右侧后缀图标，可选 / Right suffix icon, optional
  final Widget? suffixIcon;


  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 65.gw,
        padding: EdgeInsets.fromLTRB(15.gw, 0, 15.gw, 5.gw),
        decoration: const BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/images/mine/btn_bg_mine_wallet.png"), fit: BoxFit.fill),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              iconPath,
              width: iconSize?.width ?? 34.gw,
              height: iconSize?.height ?? 29.gw,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 12.gw),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  AneText(
                    title,
                    style: context.textTheme.title,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  SizedBox(height: 2.gw),
                  AutoSizeText(
                    value,
                    style: context.textTheme.secondary.fs20.w700.ffAne,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
            if (suffixIcon != null ) ...[
              SizedBox(width: 3.gw),
              suffixIcon!,
            ],
          ],
        ),
      ),
    );
  }
}
