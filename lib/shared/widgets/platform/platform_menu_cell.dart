import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class PlatformMenuCell extends StatelessWidget {
  final String title;
  final String iconUrl;
  final bool isAll;
  final VoidCallback onTap;

  const PlatformMenuCell({
    super.key,
    required this.title,
    required this.iconUrl,
    required this.isAll,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(color: context.colorTheme.foregroundColor, borderRadius: BorderRadius.circular(8.gw)),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
              left: 6,
              right: 6,
              top: 6,
              bottom: 25,
              child: AspectRatio(
                aspectRatio: 97 / 82,
                child: Container(
                  decoration: BoxDecoration(
                    color: context.colorTheme.borderC,
                    borderRadius: BorderRadius.circular(4.gw),
                  ),
                  alignment: Alignment.center,
                  padding: EdgeInsets.all(10.gw),
                  child: isAll
                      ? SvgPicture.asset(
                          "assets/images/home/<USER>",
                    colorFilter: ColorFilter.mode(context.theme.primaryColor, BlendMode.srcIn),
                    width: 32.gw,
                    height: 31.gw,
                        )
                      : AppImage(
                          imageUrl: iconUrl,
                          placeholder: Image.asset("assets/images/home/<USER>"),
                          fit: BoxFit.contain,
                        ),
                ),
              ),
            ),
            Positioned.fill(
                child: Image.asset(
              "assets/images/home/<USER>",
              fit: BoxFit.fill,
            )),
            Positioned(
              bottom: 2,
              left: 2,
              right: 2,
              child: Container(
                alignment: Alignment.center,
                child: AneText(
                  title,
                  maxLines: 1,
                  style: context.textTheme.primary,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
