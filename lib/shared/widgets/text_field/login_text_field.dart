import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class LoginTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String hintText;
  final String? prefixIconPath;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType keyboardType;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final bool autofocus;
  final int? maxLength;
  final bool enabled;

  const LoginTextField({
    super.key,
    this.controller,
    required this.hintText,
    this.prefixIconPath,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.onSubmitted,
    this.inputFormatters,
    this.focusNode,
    this.autofocus = false,
    this.maxLength,
    this.enabled = true,
  });

  @override
  State<LoginTextField> createState() => _LoginTextFieldState();
}

class _LoginTextFieldState extends State<LoginTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56.gw,
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12.gw),
        border: Border.all(
          color: _isFocused 
            ? context.theme.primaryColor 
            : Colors.transparent,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        obscureText: widget.obscureText,
        keyboardType: widget.keyboardType,
        onChanged: widget.onChanged,
        onSubmitted: widget.onSubmitted,
        inputFormatters: widget.inputFormatters,
        autofocus: widget.autofocus,
        maxLength: widget.maxLength,
        enabled: widget.enabled,
        style: context.textTheme.primary.copyWith(
          color: Colors.white,
          fontSize: 16.gw,
        ),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: context.textTheme.secondary.copyWith(
            color: Colors.white.withOpacity(0.6),
            fontSize: 16.gw,
          ),
          prefixIcon: widget.prefixIconPath != null
            ? Padding(
                padding: EdgeInsets.all(16.gw),
                child: Image.asset(
                  widget.prefixIconPath!,
                  width: 20.gw,
                  height: 20.gw,
                  color: Colors.white.withOpacity(0.8),
                ),
              )
            : null,
          suffixIcon: widget.suffixIcon,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: widget.prefixIconPath != null ? 0 : 16.gw,
            vertical: 16.gw,
          ),
          counterText: '', // Hide character counter
        ),
      ),
    );
  }
}
