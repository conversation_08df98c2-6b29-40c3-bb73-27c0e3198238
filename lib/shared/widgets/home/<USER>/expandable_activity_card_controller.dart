import 'package:flutter/material.dart';

/// 可展开活动卡片控制器 / Expandable activity card controller
class HomeExpandableActivityCardController extends ChangeNotifier {
  bool _isExpanded = false;

  /// 是否展开 / Is expanded
  bool get isExpanded => _isExpanded;

  /// 展开卡片 / Expand card
  void expand() {
    if (!_isExpanded) {
      _isExpanded = true;
      notifyListeners();
    }
  }

  /// 收起卡片 / Collapse card
  void collapse() {
    if (_isExpanded) {
      _isExpanded = false;
      notifyListeners();
    }
  }

  /// 切换展开状态 / Toggle expanded state
  void toggle() {
    _isExpanded = !_isExpanded;
    notifyListeners();
  }

  /// 设置展开状态 / Set expanded state
  void setExpanded(bool expanded) {
    if (_isExpanded != expanded) {
      _isExpanded = expanded;
      notifyListeners();
    }
  }
}