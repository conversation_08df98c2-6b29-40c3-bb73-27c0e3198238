import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

class HomeGameCell extends StatelessWidget {
  final String title;
  final String iconUrl;
  final String platIcon;
  final BoxFit imageFit;
  final bool isFav;
  final VoidCallback onTap;
  final VoidCallback onTapFav;

  const HomeGameCell({
    super.key,
    required this.title,
    required this.iconUrl,
    this.imageFit = BoxFit.cover,
    required this.isFav,
    required this.onTap,
    required this.onTapFav,
    required this.platIcon,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage("assets/images/home/<USER>"),
            fit: BoxFit.fill,
          ),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  margin: const EdgeInsets.fromLTRB(4, 4, 4, 0),
                  child: AspectRatio(
                      aspectRatio: 1,
                      child: AppImage(
                        imageUrl: iconUrl,
                        fit: imageFit,
                        radius: 6.gw,
                      )),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.gw),
                    child: Text(
                      title,
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: context.textTheme.title,
                    ),
                  ),
                ),
              ],
            ),
            Positioned(left: 3.gw, top: 3.gw, child: _buildIcon()),
            Positioned(
                right: 0,
                top: 0,
                child: _buildFavItem(isFav: isFav, onTap: onTapFav)),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      width: 32.gw,
      height: 16.gw,
      padding: EdgeInsets.symmetric(horizontal: 6.gw,vertical: 1.gw),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(6.gw),
          bottomRight: Radius.circular(6.gw),
        ),
      ),
      alignment: Alignment.center,
      child: AppImage(
        imageUrl: platIcon,
        fit: BoxFit.contain,
      ),
    );
  }

  _buildFavItem({required bool isFav, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 7.gw, vertical: 7.gw),
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/home/<USER>"_sel" : ""}.png",
          width: 24.gw,
          height: 24.gw,
        ),
      ),
    );
  }
}
