import 'package:flutter/material.dart';
import 'expandable_activity_card/expandable_activity_card_controller.dart';
import 'expandable_activity_card/home_expandable_activity_card.dart';

/// 使用示例 / Usage example
class HomeExpandableActivityCardExample extends StatefulWidget {
  const HomeExpandableActivityCardExample({super.key});

  @override
  State<HomeExpandableActivityCardExample> createState() => _HomeExpandableActivityCardExampleState();
}

class _HomeExpandableActivityCardExampleState extends State<HomeExpandableActivityCardExample> {
  late List<ActivityItem> _activities;
  late HomeExpandableActivityCardController _controller;
  bool _isControllerExpanded = false;
  bool _isCarouselPaused = false; // 轮播暂停状态 / Carousel pause state

  @override
  void initState() {
    super.initState();
    _controller = HomeExpandableActivityCardController();
    _controller.addListener(_onControllerChanged);
    
    final now = DateTime.now();
    _activities = [



      ActivityItem(
        id: '1',
        title: 'LUCKY',
        imageUrl: 'assets/images/home/<USER>/icon_lucky.png',
        endTime: now.add(const Duration(hours: 2, minutes: 30, seconds: 45)),
        onTap: () => _onActivityTap('LUCKY'),
      ),
      ActivityItem(
        id: '2',
        title: 'HOT',
        imageUrl: 'assets/images/home/<USER>/icon_hot.png',
        endTime: now.add(const Duration(hours: 1, minutes: 15, seconds: 30)),
        onTap: () => _onActivityTap('HOT'),
      ),
      ActivityItem(
        id: '3',
        title: 'NEWS',
        imageUrl: 'assets/images/home/<USER>/icon_news.png',
        endTime: now.add(const Duration(hours: 5, minutes: 45, seconds: 20)),
        onTap: () => _onActivityTap('NEWS'),
      ),
      ActivityItem(
        id: '4',
        title: 'BONUS',
        imageUrl: 'assets/images/home/<USER>/icon_bonus.png',
        endTime: now.add(const Duration(hours: 3, minutes: 20, seconds: 10)),
        onTap: () => _onActivityTap('BONUS'),
      ),
    ];
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    setState(() {
      _isControllerExpanded = _controller.isExpanded;
    });
  }

  void _onActivityTap(String activityName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('点击了 $activityName 活动')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('活动卡片示例'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            
            // 基本用法 / Basic usage
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '基本用法（默认收起）',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities,
            ),
            
            const SizedBox(height: 30),
            
            // 初始展开 / Initially expanded
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '初始展开状态',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities,
              initiallyExpanded: true,
            ),
            
            const SizedBox(height: 30),
            
            // 自定义尺寸 / Custom size
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '自定义尺寸',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities,
              collapsedWidth: 200,
              expandedWidth: 400,
              height: 80,
            ),
            
            const SizedBox(height: 30),
            
            // 外部控制器示例 / External controller example
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '外部控制器示例',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            
            // 控制按钮 / Control buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: () => _controller.expand(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('展开'),
                      ),
                      ElevatedButton(
                        onPressed: () => _controller.collapse(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('收起'),
                      ),
                      ElevatedButton(
                        onPressed: () => _controller.toggle(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('切换'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // 轮播控制按钮 / Carousel control buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _isCarouselPaused = !_isCarouselPaused;
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isCarouselPaused ? Colors.orange : Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                        child: Text(_isCarouselPaused ? '恢复轮播' : '暂停轮播'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 5),
            
            // 状态显示 / Status display
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Text(
                    '当前状态：${_isControllerExpanded ? "展开" : "收起"}',
                    style: const TextStyle(color: Colors.yellow, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    '轮播状态：${_isCarouselPaused ? "暂停" : "运行"}',
                    style: const TextStyle(color: Colors.cyan, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            
            HomeExpandableActivityCard(
              activities: _activities,
              controller: _controller,
              isPaused: _isCarouselPaused, // 传入暂停状态 / Pass pause state
              onExpandedChanged: (expanded) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('卡片状态变为：${expanded ? "展开" : "收起"}'),
                    duration: const Duration(milliseconds: 800),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 30),
            
            // 单个活动 / Single activity
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '单个活动（不可展开）',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities.take(1).toList(),
            ),
            
            const SizedBox(height: 30),
            
            // 少量活动 / Few activities
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '少量活动（2个，智能宽度）',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities.take(2).toList(),
            ),
            
            const SizedBox(height: 30),
            
            // 中等活动 / Medium activities
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '中等活动（4个，智能宽度）',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 10),
            HomeExpandableActivityCard(
              activities: _activities.take(4).toList(),
            ),
            
            const SizedBox(height: 50),
            
            // 使用说明 / Usage instructions
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '使用说明：',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 默认收起状态：115x72，贴右侧显示\n'
                    '• 智能展开宽度：根据活动数量自动调整\n'
                    '• 单个活动：不可展开，只显示收起状态\n'
                    '• 多个活动：可展开，最大宽度358\n'
                    '• 自动轮播：每3秒切换，丝滑动画效果\n'
                    '• 外部暂停控制：通过 isPaused 参数控制轮播\n'
                    '• 点击左侧指示器可以展开/收起\n'
                    '• 向左拖拽展开，向右拖拽收起\n'
                    '• 收起状态：整个区域都支持拖拽（多活动时）\n'
                    '• 展开状态：只有左侧区域支持拖拽\n'
                    '• 支持外部控制器：expand()、collapse()、toggle()\n'
                    '• 支持状态变化回调：onExpandedChanged\n'
                    '• 支持实时倒计时显示\n'
                    '• 每个活动都可以设置点击回调',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }
}
