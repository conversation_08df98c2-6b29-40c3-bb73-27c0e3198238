import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'expandable_activity_card_controller.dart';



/// 活动数据模型 / Activity data model
class ActivityItem {
  final String id;
  final String title;
  final String imageUrl;
  final DateTime? endTime; // 改为结束时间，便于计算倒计时 / Changed to end time for countdown calculation
  final VoidCallback? onTap;

  const ActivityItem({
    required this.id,
    required this.title,
    required this.imageUrl,
    this.endTime,
    this.onTap,
  });

  /// 获取剩余时间 / Get remaining time
  Duration? get remainingTime {
    if (endTime == null) return null;
    final now = DateTime.now();
    final remaining = endTime!.difference(now);
    return remaining.isNegative ? Duration.zero : remaining;
  }
}

/// 首页活动伸缩卡片 / Home expandable activity card
class HomeExpandableActivityCard extends StatefulWidget {
  /// 活动列表 / Activity list
  final List<ActivityItem> activities;

  /// 外部控制器 / External controller
  final HomeExpandableActivityCardController? controller;

  /// 初始是否展开 / Initially expanded
  final bool initiallyExpanded;

  /// 展开宽度 / Expanded width
  final double expandedWidth;

  /// 收起宽度 / Collapsed width
  final double collapsedWidth;

  /// 固定高度 / Fixed height
  final double height;

  /// 拖拽触发距离 / Drag trigger distance
  final double dragThreshold;

  /// 展开状态变化回调 / Expanded state change callback
  final ValueChanged<bool>? onExpandedChanged;
  
  /// 是否暂停轮播 / Whether to pause carousel
  final bool isPaused;

  const HomeExpandableActivityCard({
    super.key,
    required this.activities,
    this.controller,
    this.initiallyExpanded = false,
    this.expandedWidth = 358,
    this.collapsedWidth = 115,
    this.height = 72,
    this.dragThreshold = 30,
    this.onExpandedChanged,
    this.isPaused = false,
  });

  @override
  State<HomeExpandableActivityCard> createState() => _HomeExpandableActivityCardState();
}

class _HomeExpandableActivityCardState extends State<HomeExpandableActivityCard> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late ScrollController _scrollController;
  Timer? _countdownTimer; // 倒计时定时器 / Countdown timer
  Timer? _carouselTimer; // 轮播定时器 / Carousel timer

  // 轮播相关状态 / Carousel related state
  late AnimationController _carouselAnimationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elasticAnimation; // 弹性动画 / Elastic animation
  int _currentActivityIndex = 0; // 当前显示的活动索引 / Current activity index

  bool _isExpanded = false;
  double _dragStartY = 0;
  bool _isDragging = false;

  /// 单个活动项的宽度 / Width of single activity item
  static const double _itemWidth = 60.0;

  /// 活动项间距 / Activity item spacing
  static const double _itemSpacing = 8.0;

  /// 左侧拖拽区域宽度 / Left drag area width
  static const double _dragAreaWidth = 30.0;

  /// 左右内边距 / Left and right padding
  static const double _horizontalPadding = 8.0;

  /// 自定义贝塞尔曲线：加速后匀速 / Custom bezier curve: accelerate then linear
  static const Curve _customBezierCurve = Cubic(0.4, 0.0, 0.2, 1.0);
  
  /// 弹性曲线：先放大到1.2倍再回到1.0倍 / Elastic curve: scale to 1.2x then back to 1.0x
  static const Curve _elasticCurve = Curves.elasticOut;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _scrollController = ScrollController();

    // 展开/收起动画控制器 / Expand/collapse animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 轮播动画控制器 / Carousel animation controller
    _carouselAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800), // 增加动画时长以容纳复杂动画 / Increase duration for complex animation
      vsync: this,
    );

    // 缩小阶段动画：0-0.3时间段，从1.0缩小到0.7 / Scale down phase: 0-0.3 timeframe, scale from 1.0 to 0.7
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.7,
    ).animate(CurvedAnimation(
      parent: _carouselAnimationController,
      curve: const Interval(0.0, 0.3, curve: _customBezierCurve),
    ));

    // 弹性放大阶段动画：0.3-1.0时间段，从0.7放大到1.2再回到1.0 / Elastic scale up phase: 0.3-1.0 timeframe
    _elasticAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.7, end: 1.2).chain(
          CurveTween(curve: _customBezierCurve),
        ),
        weight: 40, // 40% 的时间用于放大到1.2 / 40% time for scaling to 1.2
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0).chain(
          CurveTween(curve: _elasticCurve),
        ),
        weight: 60, // 60% 的时间用于弹性回到1.0 / 60% time for elastic back to 1.0
      ),
    ]).animate(CurvedAnimation(
      parent: _carouselAnimationController,
      curve: const Interval(0.3, 1.0, curve: Curves.linear),
    ));

    // 如果有外部控制器，使用控制器的状态 / If external controller exists, use controller state
    if (widget.controller != null) {
      _isExpanded = widget.controller!.isExpanded;
      widget.controller!.addListener(_onControllerChanged);
    }

    if (_isExpanded) {
      _animationController.value = 1.0;
    }

    // 启动倒计时定时器 / Start countdown timer
    _startCountdownTimer();
    
    // 启动轮播定时器（仅在收起状态且有多个活动时）/ Start carousel timer (only when collapsed and has multiple activities)
    _startCarouselTimer();
  }

  @override
  void didUpdateWidget(HomeExpandableActivityCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果活动数据变化，重置轮播索引 / Reset carousel index if activities changed
    if (oldWidget.activities.length != widget.activities.length) {
      _currentActivityIndex = 0;
      _updateCarouselState(); // 重新评估轮播状态 / Re-evaluate carousel state
    } else if (_currentActivityIndex >= widget.activities.length) {
      // 确保索引不越界 / Ensure index doesn't exceed bounds
      _currentActivityIndex = widget.activities.isNotEmpty ? widget.activities.length - 1 : 0;
    }
    
    // 如果控制器变化，更新监听 / Update listener if controller changed
    if (oldWidget.controller != widget.controller) {
      oldWidget.controller?.removeListener(_onControllerChanged);
      if (widget.controller != null) {
        widget.controller!.addListener(_onControllerChanged);
        _setExpanded(widget.controller!.isExpanded);
      }
    }
    
    // 如果暂停状态变化，更新轮播状态 / Update carousel state if pause state changed
    if (oldWidget.isPaused != widget.isPaused) {
      _updateCarouselState();
    }
  }

  /// 控制器状态变化回调 / Controller state change callback
  void _onControllerChanged() {
    if (widget.controller != null && widget.controller!.isExpanded != _isExpanded) {
      _setExpanded(widget.controller!.isExpanded);
    }
  }



  /// 更新轮播状态 / Update carousel state
  void _updateCarouselState() {
    final bool shouldRunCarousel = !widget.isPaused && !_isExpanded && widget.activities.length > 1;
    
    // if (kDebugMode) {
    //   print('🎠 轮播状态更新: 暂停=${widget.isPaused}, 收起状态=${!_isExpanded}, 多活动=${widget.activities.length > 1}');
    //   print('🎠 应该运行轮播: $shouldRunCarousel');
    // }
    
    if (shouldRunCarousel) {
      _startCarouselTimer();
    } else {
      _stopCarouselTimer();
      // 如果正在进行轮播动画，停止它 / If carousel animation is running, stop it
      if (_carouselAnimationController.isAnimating) {
        _carouselAnimationController.stop();
        _carouselAnimationController.reset();
      }
    }
  }

  /// 计算实际展开宽度 / Calculate actual expanded width
  double get _actualExpandedWidth {
    if (widget.activities.isEmpty) return widget.collapsedWidth;

    // 单个活动时不展开，返回收起宽度 / Single activity doesn't expand, return collapsed width
    if (widget.activities.length == 1) {
      return widget.collapsedWidth;
    }

    // 计算所有活动项的总宽度 / Calculate total width of all activity items
    // N个活动项宽度 + (N-1)个间距
    final totalItemsWidth = (widget.activities.length * _itemWidth) + ((widget.activities.length - 1) * _itemSpacing);

    // 加上拖拽区域和左右内边距 / Add drag area and left/right padding
    final totalRequiredWidth = _dragAreaWidth + (_horizontalPadding * 2) + totalItemsWidth;

    // // 调试信息 / Debug info
    // if (kDebugMode) {
    //   print('🔍 活动数量: ${widget.activities.length}');
    //   print('🔍 项目总宽度: $totalItemsWidth');
    //   print('🔍 所需总宽度: $totalRequiredWidth');
    //   print('🔍 最大宽度: ${widget.expandedWidth}');
    //   print('🔍 实际宽度: ${totalRequiredWidth.clamp(widget.collapsedWidth, widget.expandedWidth)}');
    // }

    // 返回实际需要的宽度和最大宽度中的较小值 / Return the smaller of required width and max width
    return totalRequiredWidth.clamp(widget.collapsedWidth, widget.expandedWidth);
  }

  /// 是否可以展开 / Whether can expand
  bool get _canExpand {
    return widget.activities.length > 1;
  }

  /// 启动倒计时定时器 / Start countdown timer
  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // 强制重建以更新倒计时显示 / Force rebuild to update countdown display
        });
      }
    });
  }

  /// 启动轮播定时器 / Start carousel timer
  void _startCarouselTimer() {
    _stopCarouselTimer(); // 先停止现有的定时器 / Stop existing timer first
    
    // 只有在满足所有条件时才启动轮播 / Only start carousel when all conditions are met
    if (!widget.isPaused && !_isExpanded && widget.activities.length > 1) {
      // if (kDebugMode) {
      //   print('🎠 启动轮播定时器');
      // }
      
      _carouselTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        if (mounted && !widget.isPaused && !_isExpanded) {
          _switchToNextActivity();
        } else {
          // 如果条件不满足，停止定时器 / Stop timer if conditions not met
          timer.cancel();
          // if (kDebugMode) {
          //   print('🛑 轮播定时器自动停止');
          // }
        }
      });
    } else {
      // if (kDebugMode) {
      //   print('❌ 轮播启动条件不满足: 暂停=${widget.isPaused}, 收起状态=${!_isExpanded}, 多活动=${widget.activities.length > 1}');
      // }
    }
  }

  /// 停止轮播定时器 / Stop carousel timer
  void _stopCarouselTimer() {
    _carouselTimer?.cancel();
    _carouselTimer = null;
  }

  /// 切换到下一个活动 / Switch to next activity
  void _switchToNextActivity() async {
    if (widget.activities.isEmpty) return;

    final nextIndex = (_currentActivityIndex + 1) % widget.activities.length;

    // if (kDebugMode) {
    //   print('🎠 轮播切换: ${widget.activities[_currentActivityIndex].title} → ${widget.activities[nextIndex].title}');
    // }

    // 重置动画控制器 / Reset animation controller
    _carouselAnimationController.reset();
    
    // 启动完整的动画序列 / Start complete animation sequence
    _carouselAnimationController.forward().then((_) {
      // 动画完成后重置控制器，准备下次动画 / Reset controller after animation completes
      if (mounted) {
        _carouselAnimationController.reset();
        // if (kDebugMode) {
        //   print('🎬 轮播动画完成');
        // }
      }
    });
    
    // 在缩小阶段结束时切换活动（30%进度时）/ Switch activity at end of scale down phase (30% progress)
    await Future.delayed(const Duration(milliseconds: 240)); // 800ms * 0.3 = 240ms
    
    if (mounted) {
      setState(() {
        _currentActivityIndex = nextIndex;
      });
      
      // if (kDebugMode) {
      //   print('🔄 活动切换完成，当前索引: $_currentActivityIndex');
      // }
    }
  }

  @override
  void dispose() {
    // 清理定时器 / Clean up timers
    _countdownTimer?.cancel();
    _stopCarouselTimer();
    
    // 清理控制器监听 / Clean up controller listener
    widget.controller?.removeListener(_onControllerChanged);
    
    // 清理动画控制器 / Clean up animation controllers
    _animationController.dispose();
    _carouselAnimationController.dispose();
    _scrollController.dispose();
    
    super.dispose();
  }

  /// 切换展开/收起状态 / Toggle expand/collapse state
  void _toggleExpanded() {
    // 单个活动时不允许展开 / Don't allow expand for single activity
    if (!_canExpand && !_isExpanded) return;
    _setExpanded(!_isExpanded);
  }

  /// 设置展开状态 / Set expanded state
  void _setExpanded(bool expanded) {
    // 单个活动时不允许展开 / Don't allow expand for single activity
    if (!_canExpand && expanded) return;

    if (_isExpanded == expanded) return;

    setState(() {
      _isExpanded = expanded;
    });

    // 同步到外部控制器 / Sync to external controller
    if (widget.controller != null) {
      widget.controller!.setExpanded(_isExpanded);
    }

    // 触发回调 / Trigger callback
    widget.onExpandedChanged?.call(_isExpanded);

    // 使用统一的状态更新方法 / Use unified state update method
    _updateCarouselState();

    // 执行动画 / Execute animation
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  /// 处理拖拽开始 / Handle drag start
  void _onPanStart(DragStartDetails details) {
    _dragStartY = details.localPosition.dx; // 改为记录X坐标 / Change to record X coordinate
    _isDragging = true;
  }

  /// 处理拖拽更新 / Handle drag update
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    final deltaX = details.localPosition.dx - _dragStartY; // 改为水平拖拽 / Change to horizontal drag
    final progress = (deltaX / widget.dragThreshold).clamp(-1.0, 1.0);

    if (_isExpanded) {
      // 展开状态下向右拖拽收起 / Drag right to collapse when expanded
      if (deltaX > widget.dragThreshold) {
        _animationController.value = 1.0 - progress.abs();
      }
    } else if (_canExpand) {
      // 收起状态下向左拖拽展开（仅当可以展开时）/ Drag left to expand when collapsed (only if can expand)
      if (deltaX < -widget.dragThreshold) {
        _animationController.value = progress.abs();
      }
    }
  }

  /// 处理拖拽结束 / Handle drag end
  void _onPanEnd(DragEndDetails details) {
    if (!_isDragging) return;
    _isDragging = false;

    final deltaX = details.velocity.pixelsPerSecond.dx;

    // 根据拖拽距离和速度决定是否切换状态 / Determine state change based on drag distance and velocity
    if (deltaX.abs() > 100) {
      if (deltaX < 0 && !_isExpanded && _canExpand) {
        _toggleExpanded();
      } else if (deltaX > 0 && _isExpanded) {
        _toggleExpanded();
      } else {
        // 恢复原状态 / Restore original state
        if (_isExpanded) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }
      }
    } else {
      // 恢复原状态 / Restore original state
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.activities.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final width = widget.collapsedWidth + (_actualExpandedWidth - widget.collapsedWidth) * _animation.value;

        return Align(
          alignment: Alignment.centerRight, // 贴右侧 / Align to right
          child: SizedBox(
            width: width.gw,
            height: widget.height.gw,
            child: Stack(
              children: [
                // 活动列表 / Activity list
                Positioned.fill(
                  child: _buildActivityList(),
                ),

                // 拖拽控制区域 / Drag control area
                if (_canExpand || _isExpanded) // 只有可以展开或已展开时才显示拖拽区域
                  // 展开状态：只有左侧区域可以拖拽 / Expanded state: only left area draggable
                  Positioned(
                    left: 0.5,
                    top: 0,
                    bottom: 0,
                    width: 30.gw,
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: _toggleExpanded,
                      onPanStart: _onPanStart,
                      onPanUpdate: _onPanUpdate,
                      onPanEnd: _onPanEnd,
                      child: _buildDragIndicator(), // 透明覆盖层 / Transparent overlay
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建活动列表 / Build activity list
  Widget _buildActivityList() {
    if (_animation.value < 0.1) {
      // 收起状态，显示当前轮播的活动 / Collapsed state, show current carousel activity
      final safeIndex = _currentActivityIndex.clamp(0, widget.activities.length - 1);
      final currentActivity = widget.activities[safeIndex];
      return _buildCollapsedActivity(currentActivity);
    }

    // 展开状态，显示水平滚动列表 / Expanded state, show horizontal scrollable list
    return Container(
      margin: EdgeInsets.only(left: 30.gw), // 为拖拽区域留空间 / Leave space for drag area
      child: Stack(
        children: [
          // 背景图片，使用 fit: BoxFit.cover 避免变形 / Background image with BoxFit.cover to avoid distortion
          Positioned.fill(
            child: ClipRRect(
              child: Image.asset(
                "assets/images/home/<USER>/bg_expand.png",
                fit: BoxFit.cover, // 使用 cover 避免变形 / Use cover to avoid distortion
                alignment: Alignment.centerLeft, // 从左侧对齐 / Align from left
              ),
            ),
          ),

          // 如果图片不够宽，用渐变色作为补充 / Use gradient as fallback if image is not wide enough
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    const Color(0xFF1A1A1A).withOpacity(0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),

          // 活动列表 / Activity list
          Positioned.fill(
            child: ListView.separated(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 8.gw),
              itemCount: widget.activities.length,
              itemBuilder: (context, index) {
                return _buildExpandedActivityItem(widget.activities[index]);
              },
              separatorBuilder: (context, index) => SizedBox(width: 8.gw),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建收起状态的活动（115x72）/ Build collapsed activity (115x72)
  Widget _buildCollapsedActivity(ActivityItem activity) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      // 确保整个区域都能接收手势事件
      onTap: () {
        // 如果活动有点击回调，优先执行活动回调，否则展开（仅当可以展开时）
        if (activity.onTap != null) {
          activity.onTap!();
        } else if (_canExpand) {
          _toggleExpanded();
        }
      },
      // 只有可以展开时才启用拖拽手势 / Only enable drag gestures when can expand
      onPanStart: _canExpand ? _onPanStart : null,
      onPanUpdate: _canExpand ? _onPanUpdate : null,
      onPanEnd: _canExpand ? _onPanEnd : null,
      child: Container(
        margin: EdgeInsets.only(left: 30.gw),
        decoration: const BoxDecoration(
            image: DecorationImage(image: AssetImage("assets/images/home/<USER>/bg_collapse.png"), fit: BoxFit.fill)),
        child: Center(
          child: AnimatedBuilder(
            animation: _carouselAnimationController,
            builder: (context, child) {
              // 计算当前应该使用的缩放值 / Calculate current scale value to use
              double currentScale = 1.0;
              
              if (_carouselAnimationController.value <= 0.3) {
                // 缩小阶段：使用 _scaleAnimation / Scale down phase: use _scaleAnimation
                currentScale = _scaleAnimation.value;
              } else {
                // 弹性放大阶段：使用 _elasticAnimation / Elastic scale up phase: use _elasticAnimation
                currentScale = _elasticAnimation.value;
              }
              
              // 添加微小的旋转效果以增强视觉效果 / Add subtle rotation for enhanced visual effect
              final rotationAngle = (_carouselAnimationController.value - 0.5) * 0.02; // 很小的旋转角度
              
              return Transform.scale(
                scale: currentScale.clamp(0.5, 1.5), // 限制缩放范围以避免极端值 / Clamp scale to avoid extreme values
                child: Transform.rotate(
                  angle: rotationAngle,
                  child: _buildExpandedActivityItem(activity, isCollapsed: true),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建展开状态的活动项 / Build expanded activity item
  Widget _buildExpandedActivityItem(ActivityItem activity, {bool isCollapsed = false}) {
    return GestureDetector(
      onTap: isCollapsed ? null : activity.onTap, // 收起状态下不处理这里的点击，由父级处理
      child: SizedBox(
        width: 60.gw,
        height: 60.gw,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            // 活动图标 / Activity icon
            SizedBox(
              width: 60.gw,
              height: 53.gw,
              child: Image.asset(
                activity.imageUrl,
                fit: BoxFit.contain,
              ),
            ),

            // 活动标题 / Activity title
            Positioned(
              bottom: 0,
              child: AneText(
                activity.title,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.gw,
                  fontWeight: FontWeight.w700,
                  shadows: List.generate(
                    8,
                    (_) => const Shadow(
                      color: Color(0xFFF3DE29),
                      blurRadius: 5,
                      offset: Offset(0, 0),
                    ),
                  ),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),

            if (activity.remainingTime != null && activity.remainingTime! > Duration.zero) ...[
              Positioned(top: 2.gw, child: _buildCountdown(activity.remainingTime!, isSmall: true)),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建倒计时 / Build countdown
  Widget _buildCountdown(Duration countdown, {bool isSmall = false}) {
    final hours = countdown.inHours;
    final minutes = countdown.inMinutes % 60;
    final seconds = countdown.inSeconds % 60;

    final timeText = '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 4.gw : 6.gw,
        vertical: isSmall ? 2.gw : 3.gw,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFFF4444),
        borderRadius: BorderRadius.circular(isSmall ? 8.gw : 10.gw),
      ),
      child: Text(
        timeText,
        style: TextStyle(
          color: Colors.white,
          fontSize: isSmall ? 8.gw : 10.gw,
          fontWeight: FontWeight.w600,
          fontFamily: 'monospace',
        ),
      ),
    );
  }

  /// 构建拖拽指示器 / Build drag indicator
  Widget _buildDragIndicator() {
    return Container(
      width: 24.gw,
      height: widget.height.gw,
      alignment: Alignment.centerRight,
      child: Image.asset(
        "assets/images/home/<USER>/btn_drag_indicator.png",
        width: 24.gw,
        height: 47.25.gw,
      ),
    );
  }
}
