import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class FilterableCountryPicker {
  /// 显示带筛选功能的国家选择器 - Popup 方式
  static Future<Country?> show({
    required BuildContext context,
    required GlobalKey anchorKey,
    Country? selectedCountry,
    String? title,
  }) {
    // 获取锚点位置
    final RenderBox? renderBox = anchorKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return Future.value(null);

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenSize = MediaQuery.of(context).size;

    // 计算弹出框位置
    final double left = position.dx;
    final double top = position.dy + size.height - 10.gw;
    final double maxHeight = screenSize.height - top - 50.gw;

    return Navigator.of(context).push<Country>(
      PageRouteBuilder<Country>(
        opaque: false,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Stack(
            children: [
              // 透明背景，点击关闭
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                ),
              ),
              // 弹出框
              Positioned(
                left: left,
                top: top,
                child: Material(
                  color: Colors.transparent,
                  child: FadeTransition(
                    opacity: animation,
                    child: ScaleTransition(
                      scale: Tween<double>(begin: 0.95, end: 1.0).animate(
                        CurvedAnimation(parent: animation, curve: Curves.easeOut),
                      ),
                      child: _FilterableCountryPickerContent(
                        selectedCountry: selectedCountry,
                        title: title,
                        maxHeight: maxHeight,
                        maxWidth: 280.gw,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        transitionDuration: const Duration(milliseconds: 200),
      ),
    );
  }
}

class _FilterableCountryPickerContent extends StatefulWidget {
  final Country? selectedCountry;
  final String? title;
  final double maxHeight;
  final double maxWidth;

  const _FilterableCountryPickerContent({
    this.selectedCountry,
    this.title,
    required this.maxHeight,
    required this.maxWidth,
  });

  @override
  State<_FilterableCountryPickerContent> createState() =>
      _FilterableCountryPickerContentState();
}

class _FilterableCountryPickerContentState
    extends State<_FilterableCountryPickerContent> {
  final TextEditingController _searchController = TextEditingController();
  List<Country> _allCountries = [];
  List<Country> _filteredCountries = [];
  bool _isLoading = true;
  Country? _selectedCountry;

  @override
  void initState() {
    super.initState();
    _selectedCountry = widget.selectedCountry;
    _loadCountries();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCountries() async {
    try {
      final countries = await CountryService.instance.getCountries();
      if (mounted) {
        setState(() {
          _allCountries = countries;
          _filteredCountries = countries;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = _allCountries;
      } else {
        _filteredCountries = _allCountries.where((country) {
          final lowerQuery = query.toLowerCase();
          return country.name.toLowerCase().contains(lowerQuery) ||
              country.code.toLowerCase().contains(lowerQuery) ||
              country.areaCode.toString().contains(query);
        }).toList();
      }
    });
  }

  void _selectCountry(Country country) {
    Navigator.of(context).pop(country);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.maxWidth,
      constraints: BoxConstraints(
        maxHeight: widget.maxHeight,
        minHeight: 300.gw,
      ),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10.gw,
            offset: Offset(0, 4.gw),
          ),
        ],
        border: Border.all(
          color: context.colorTheme.borderA,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 搜索框
          _buildSearchField(),

          // 国家列表
          Expanded(
            child: _buildCountryList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      margin: EdgeInsets.all(12.gw),
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      height: 40.gw,
      decoration: BoxDecoration(
        color: context.theme.appBarTheme.backgroundColor,

        borderRadius: BorderRadius.circular(8.gw),
        border: Border.all(
          color: context.theme.dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            size: 16.gw,
            color: context.colorTheme.textSecondary,
          ),
          SizedBox(width: 8.gw),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: context.textTheme.primary.copyWith(fontSize: 14.gw),
              decoration: InputDecoration(
                hintText: 'search_country'.tr(),
                hintStyle: context.textTheme.highlight.copyWith(fontSize: 14.gw),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
            ),
          ),
          if (_searchController.text.isNotEmpty)
            GestureDetector(
              onTap: () {
                _searchController.clear();
              },
              child: Icon(
                Icons.clear,
                size: 16.gw,
                color: context.colorTheme.textSecondary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCountryList() {
    if (_isLoading) {
      return SizedBox(
        height: 100.gw,
        child: Center(
          child: CircularProgressIndicator(
            color: context.theme.primaryColor,
            strokeWidth: 2,
          ),
        ),
      );
    }

    if (_filteredCountries.isEmpty) {
      return SizedBox(
        height: 100.gw,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 32.gw,
                color: context.colorTheme.textSecondary,
              ),
              SizedBox(height: 8.gw),
              Text(
                'no_countries_found'.tr(),
                style: context.textTheme.secondary.copyWith(fontSize: 12.gw),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
      itemCount: _filteredCountries.length,
      itemBuilder: (context, index) {
        final country = _filteredCountries[index];
        final isSelected = _selectedCountry?.code == country.code;

        return _buildCountryItem(country, isSelected);
      },
    );
  }

  Widget _buildCountryItem(Country country, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectCountry(country),
      child: Container(
        margin: EdgeInsets.only(bottom: 2.gw),
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gw),
        decoration: BoxDecoration(
          color: isSelected 
              ? context.theme.primaryColor.withOpacity(0.1) 
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.gw),
        ),
        child: Row(
          children: [
            // 国旗
            Text(
              country.flag,
              style: TextStyle(fontSize: 18.gw),
            ),
            SizedBox(width: 8.gw),

            // 国家名称
            Expanded(
              child: Text(
                country.name,
                style: context.textTheme.primary.copyWith(
                  fontSize: 14.gw,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? context.theme.primaryColor : null,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            SizedBox(width: 4.gw),

            // 区号
            Text(
              '+${country.areaCode}',
              style: context.textTheme.primary.copyWith(
                fontSize: 14.gw,
                fontWeight: FontWeight.w600,
                color: isSelected ? context.theme.primaryColor : context.colorTheme.textSecondary,
              ),
            ),

            // 选中指示器
            if (isSelected) ...[
              SizedBox(width: 8.gw),
              Icon(
                Icons.check,
                size: 16.gw,
                color: context.theme.primaryColor,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
