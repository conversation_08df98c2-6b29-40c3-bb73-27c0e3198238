import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

enum SearchHistoryCellType {
  history,
  hot,
}

class SearchHistoryCell extends StatelessWidget {
  final SearchHistoryCellType type;
  final String title;
  final GestureTapCallback onTapCell;
  final GestureTapCallback? onTapDelete;

  const SearchHistoryCell({
    super.key,
    required this.title,
    required this.type,
    required this.onTapCell,
    this.onTapDelete,
  });

  @override
  Widget build(BuildContext context) {
    String prefixIcon = "assets/images/video/icon_video_history_item.svg";
    if (type == SearchHistoryCellType.hot) prefixIcon = "assets/images/video/icon_video_search.svg";
    const deleteIcon = "assets/images/video/icon_video_delete_item.svg";
    return InkWell(
      onTap: onTapCell,
      child: SizedBox(
        height: 40.gw,
        child: Row(
          children: [
            SvgPicture.asset(prefixIcon, width: 15.gw, height: 14.gw),
            SizedBox(width: 10.gw),
            Expanded(child: AneText(title, style: context.textTheme.highlight, maxLines: 1,)),
            if (type == SearchHistoryCellType.history) ...[
              InkWell(
                onTap: onTapDelete,
                child: Container(
                  width: 35.gw,
                  height: double.infinity,
                  alignment: Alignment.bottomRight,
                  child: SvgPicture.asset(deleteIcon, width: 11.5.gw, height: 14.gw),
                ),
              )
            ]
          ],
        ),
      ),
    );
  }
}
