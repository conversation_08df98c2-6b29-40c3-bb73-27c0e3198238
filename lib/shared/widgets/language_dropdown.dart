import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/string_util.dart';

class LanguageDropdown extends StatelessWidget {
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final double? height;
  final EdgeInsets? padding;

  const LanguageDropdown({
    super.key,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<LocaleEntity>(
      onSelected: (LocaleEntity locale) => LocaleUtil().setCurrentLocale(locale),
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      itemBuilder: (BuildContext context) => LocaleUtil().supportLocaleList.map((locale) {
        final isSelected = LocaleUtil().currentLocale == locale;
        return PopupMenuItem<LocaleEntity>(
          value: locale,
          child: Row(
            children: [
              Text(
                StringUtil.countryCodeToEmoji(locale.countryCode),
                style: TextStyle(fontSize: 16.gw),
              ),
              SizedBox(width: 8.gw),
              Text(
                locale.name,
                style: isSelected ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        );
      }).toList(),
      child: Container(
        height: height ?? 32.gw,
        padding: padding ?? EdgeInsets.symmetric(horizontal: 11.gw),
        decoration: BoxDecoration(
          color: backgroundColor ?? context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(10.gw),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              StringUtil.countryCodeToEmoji(LocaleUtil().currentLocale.countryCode),
              style: TextStyle(fontSize: 16.gw),
            ),
            SizedBox(width: 4.gw),
            Text(
              LocaleUtil().currentLocale.name,
              style: (textColor != null
                  ? TextStyle(color: textColor, fontSize: 13.gw, fontWeight: FontWeight.w500)
                  : context.textTheme.primary.fs13.w500),
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.arrow_drop_down,
              size: 20.gw,
              color: iconColor ?? context.colorTheme.textTitle,
            ),
          ],
        ),
      ),
    );
  }

}
