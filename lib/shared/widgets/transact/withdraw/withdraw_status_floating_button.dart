import 'package:flutter/material.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';

class WithdrawStatusFloatingButton extends StatefulWidget {
  final String title;

  const WithdrawStatusFloatingButton({super.key, required this.title});

  @override
  State createState() => WithdrawStatusFloatingButtonState();
}

class WithdrawStatusFloatingButtonState extends State<WithdrawStatusFloatingButton> with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _controller;

  late Animation<double> _turns; // 箭头旋转
  double _currentTurn = 0.0;

  late Animation<double> _opacity; // 箭头透明度

  final String expandedIconPath = 'assets/images/transact/btn_floating_withdraw.svg'; // 状态图标
  final String waitingIconPath = 'assets/images/transact/icon_waiting_withdraw.png'; // 出款图标

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _turns = Tween<double>(begin: 1.0, end: 0.5).animate(_controller);

    _opacity = Tween<double>(begin: 0.0, end: 0.0).animate(_controller);
  }

  // 定义关闭方法
  void close() {
    if (_isExpanded) {
      _toggleExpand();
    }
  }

  void _toggleExpand() {
    final oldTurn = _currentTurn;
    _currentTurn += 0.5; // 每次点击增加 0.5 turns = 180°

    _turns = Tween<double>(begin: oldTurn, end: _currentTurn).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );

    _opacity = Tween<double>(
      begin: _isExpanded ? 0.6 : 1.0,
      end: _isExpanded ? 1.0 : 0.6,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller
      ..reset()
      ..forward().then((_) {

        setState(() {
          _isExpanded = !_isExpanded;
        });
      });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleExpand,
      child: AnimatedContainer(
        padding: EdgeInsets.symmetric(horizontal: 6.gw),
        duration: const Duration(milliseconds: 300),
        width: _isExpanded ? 138.gw : 32.gw,
        height: 36.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.gw),
            bottomLeft: Radius.circular(8.gw),
          ),
          border: Border(
            top:    BorderSide(width: 1, color: Colors.black.withOpacity(0.04)),
            bottom: BorderSide(width: 1, color: Colors.black.withOpacity(0.04)),
            left:   BorderSide(width: 1, color: Colors.black.withOpacity(0.04)),
          ),
          color: context.theme.cardColor,
        ),
        child: InnerShadow(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FadeTransition(
                opacity: _opacity,
                child: RotationTransition(
                  turns: _turns,
                  child: SvgPicture.asset(
                    expandedIconPath,
                    width: 16.gw,
                    height: 16.gw,
                  ),
                ),
              ),
              if (_isExpanded) ...[
                SizedBox(width: 4.gw),
                Image.asset(
                  waitingIconPath,
                  width: 24.gw,
                  height: 24.gw,
                ),

                SizedBox(width: 4.gw),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      sl<NavigatorService>().push(AppRouter.transactWithdrawProgress);
                      _toggleExpand();
                    },
                    child: Container(
                      height: double.infinity,
                      alignment: Alignment.center,
                      child: Text(
                        widget.title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.fs,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                      ),
                    ),
                  ),
                )
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
