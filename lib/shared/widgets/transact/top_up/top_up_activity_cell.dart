import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class TopUpActivityCell extends StatelessWidget {
  final String title;
  final String description;
  final bool isSelected;
  final GestureTapCallback? onTap;


  const TopUpActivityCell({
    super.key,
    required this.title,
    required this.description,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(20.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.gw),
          color: isSelected ? const Color(0xff1c1a12) : context.colorTheme.highlightForeground,
          border: isSelected ? Border.all(color: context.theme.primaryColor, width: 0.5) : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    title,
                    style: context.textTheme.secondary.fs20.w500
                        .copyWith(color: isSelected ? context.colorTheme.textPrimary : null),
                  ),
                  SizedBox(height: 5.gw),
                  AneText(
                    description,
                    style: context.textTheme.title.fs20.w500,
                  ),
                ],
              ),
            ),
            SizedBox(width: 10.gw),
            SvgPicture.asset(
              isSelected
                  ? "assets/images/checkmark/icon_checkmark_circle_selected.svg"
                  : "assets/images/checkmark/icon_checkmark_circle_unselected.svg",
              width: 24.gw,
              height: 24.gw,
            )
          ],
        ),
      ),
    );
  }
}
