import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:text_scroll/text_scroll.dart';
import 'package:wd/shared/widgets/app_image.dart';

import '../transact_tag_widget.dart';

class TopUpWayCell extends StatelessWidget {
  final String title;
  final String icon;
  final bool isSelected;
  final bool isRecommended;
  final Color selectedBorderColor;
  final String? payWayTag;

  const TopUpWayCell({
    super.key,
    required this.title,
    required this.icon,
    required this.isSelected,
    this.isRecommended = false,
    Color? selectedBorderColor,
    this.payWayTag,
  }) : selectedBorderColor = selectedBorderColor ?? const Color(0xffCDB296);

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned.fill(
          child: Container(
            margin: EdgeInsets.only(bottom: 2.gw),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: isSelected ? context.colorTheme.foregroundColor : context.theme.cardColor,
              borderRadius: BorderRadius.circular(6.gw),
              border:  Border.all(color: isSelected ? context.theme.primaryColor : context.colorTheme.foregroundColor, width: 0.5),

            ),
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: 20),
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLogoWidget(context),
                  SizedBox(height: 10.gw),
                  Flexible(child: _buildTitleWidget(context)),
                ],
              ),
            ),
          ),
        ),

        if (payWayTag != null && payWayTag!.isNotEmpty)
          Positioned(top: -8.gw, right: -6, child: TransactTagWidget(title: payWayTag!))
      ],
    );
  }

  Widget _buildLogoWidget(BuildContext context) {
    return Container(
      width: 40.gw,
      height: 39.gw,
      padding: EdgeInsets.all(6.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(6.gw),
      ),
      alignment: Alignment.center,
      child: AppImage(
        imageUrl: icon,
        width: double.infinity,
      ),
    );
  }

  Widget _buildTitleWidget(BuildContext context) {
    return TextScroll(
      title,
      mode: TextScrollMode.endless,
      velocity: const Velocity(pixelsPerSecond: Offset(20, 0)),
      delayBefore: const Duration(milliseconds: 500),
      style: context.textTheme.secondary.fs16.w500,
    );
  }
}
