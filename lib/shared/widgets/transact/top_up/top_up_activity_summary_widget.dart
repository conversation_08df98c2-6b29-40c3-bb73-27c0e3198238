import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/row/text_row.dart';

class TopUpActivitySummaryVM {
  final double depositAmount;
  final double bonus;
  final double total;
  final String? bonusRateText; // 例如 "10%"（仅 isRatio 时展示）
  final bool notMet;

  TopUpActivitySummaryVM({
    required this.depositAmount,
    required this.bonus,
    required this.total,
    required this.bonusRateText,
    required this.notMet,
  });

  factory TopUpActivitySummaryVM.from({
    required TopUpActivity model,
    required double inputAmount,
  }) {
    final bonus = calcBonus(model: model, inputAmount: inputAmount);
    final notMet = bonus == 0;

    String? rateText;
    if (model.isRatio) {
// 假设 ratio 为 0.1 = 10%
      final percent = (model.ratio * 100);
      rateText = "${percent.toStringAsFixed(2)}%";
    }

    return TopUpActivitySummaryVM(
      depositAmount: inputAmount,
      bonus: bonus,
      total: inputAmount + bonus,
      bonusRateText: rateText,
      notMet: notMet,
    );
  }

  static double calcBonus({
    required TopUpActivity model,
    required double inputAmount,
  }) {
    if (inputAmount < model.rcgAmount) return 0;

    if (model.isRatio) {
      final base = model.isUserAmountRatio ? inputAmount : model.rcgAmount;
      return base * model.ratio; // 比例 = 基数 * ratio（而不是金额 * 金额）
    }
    return model.fixedAmount;
  }
}

class TopUpActivitySummaryWidget extends StatelessWidget {
  final TopUpActivitySummaryVM vm;

  const TopUpActivitySummaryWidget({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    if (vm.notMet) {
      return Align(
        alignment: Alignment.centerLeft,
        child: Text(
          "tips_recharge_activity_not_met".tr(),
          style: context.textTheme.title.ffAne,
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildRow(context, title: "deposit_amount".tr(), content: vm.depositAmount.formattedMoney),
        if (vm.bonusRateText != null)
          _buildRow(context, title: "bonus_rate".tr(), content: vm.bonusRateText!)
        else
          _buildRow(context, title: "bonus_amount".tr(), content: vm.bonus.formattedMoney),
        _buildRow(context, title: "total_credited".tr(), content: vm.total.formattedMoney),
      ],
    );
  }

  Widget _buildRow(BuildContext context, {required String title, required String content}) {
    return TextRow(
      title: title,
      titleStyle: context.textTheme.title.ffAne,
      content: content,
      contentStyle: context.textTheme.title.ffAne,
    );
  }
}
