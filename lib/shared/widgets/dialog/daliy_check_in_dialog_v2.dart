import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/2_activity/activity/check_in_item_widget.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class DailyCheckInDialogV2 {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  DailyCheckInDialogV2({
    required this.onClickCheckIn,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _DailyCheckInDialogContent(
          onClickCheckIn: onClickCheckIn,
        );
      },
    );
  }
}

class _DailyCheckInDialogContent extends StatefulWidget {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  const _DailyCheckInDialogContent({
    required this.onClickCheckIn,
  });

  @override
  State<StatefulWidget> createState() => _DailyCheckInDialogContentState();
}

class _DailyCheckInDialogContentState
    extends State<_DailyCheckInDialogContent> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: EdgeInsets.all(12.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.gw),
          color: context.theme.cardColor,
        ),
        width: 400.gw, // 80% of screen width
        child: _buildDialogBox(context),
      ),
    );
  }

  Widget _buildDialogBox(BuildContext context) {
    return BlocBuilder<ActivityListCubit, ActivityListState>(
      builder: (context, state) {
        if (state.checkInModel == null) return Container();

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(state),
            SizedBox(height: 40.gw),
            _buildContent(context, list: state.checkInModel!.fullList),
          ],
        );
      },
    );
  }

  Widget _buildHeader(ActivityListState state) {
    final total = state.checkInModel?.totalDays ?? ' ';
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AneText(
              'act_daily_check_in'.tr(),
              style: context.textTheme.secondary.fs20.w500,
            ),
            SizedBox(height: 6.gw),
            Text(
              '${'act_u_have_signed_for'.tr()} $total ${'act_day'.tr()}',
              style: context.textTheme.title,
            )
          ],
        ),
        const Spacer(),
        InkWell(
          onTap: Navigator.of(context).pop,
          child: Container(
            width: 40.gw,
            height: 40.gw,
            alignment: Alignment.topRight,
            child: HotPushImage(
              imagePath: "assets/images/check_in/btn_check_in_dialog_close.png",
              width: 28.gw,
              height: 28.gw,
            ),
          ),
        )
      ],
    );
  }


  Widget _buildContent(
    BuildContext context, {
    required List<DailyCheckInItem> list,
  }) {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.only(bottom: 10.gw),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        mainAxisSpacing: 5.gw,
        crossAxisSpacing: 5.gw,
        childAspectRatio: 56 / 75,
      ),
      itemBuilder: (context, index) {
        final e = list[index];
        return CheckInItemWidget(
          model: e,
          onClickCheckIn: widget.onClickCheckIn,
        );
      },
      itemCount: list.length,
    );
  }
}
