import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/gradient_border.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class CurrencySwitchDialog {
  final BuildContext context;
  final CurrencyConfig currentCurrency;
  final List<CurrencyConfig> dataList;
  final void Function(CurrencyConfig) onClickCurrency;

  CurrencySwitchDialog(this.context,
      {required this.currentCurrency, required this.dataList, required this.onClickCurrency});

  show() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Center(
            child: Container(
              width: 362.gw,
              padding: EdgeInsets.all(24.gw),
              decoration: const BoxDecoration(
                image: DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildTitle(),
                  SizedBox(height: 32.gw),
                  _buildTips(),
                  SizedBox(height: 18.gw),
                  _buildContentWheelPicker(),
                  SizedBox(height: 18.gw),
                  _buildOperateView(),
                ],
              ),
            ),
          );
        });
  }

  int _selectedIndex = 0;

  Widget _buildTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'select'.tr(),
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 4.gw),
        AneText(
          "currency".tr(),
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  Widget _buildTips() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: AneText('switch_currency_tips'.tr(), style: context.textTheme.highlight),
    );
  }

  Widget _buildContentWheelPicker() {
    _selectedIndex = dataList.indexOf(currentCurrency);
    return StatefulBuilder(
      builder: (context, setState) {
        return SizedBox(
          height: 200.gw,
          child: CupertinoPicker(
            scrollController: FixedExtentScrollController(initialItem: _selectedIndex),
            itemExtent: 45.gw,
            backgroundColor: Colors.transparent,
            selectionOverlay: Container(
              decoration: BoxDecoration(
                border: GradientBoxBorder(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.0),
                    ],
                  ),
                  width: 1.gw,
                ),
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                  ],
                ),
              ),
            ),
            onSelectedItemChanged: (int index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            children: List.generate(dataList.length, (index) {
              final currencyModel = dataList[index];
              final isSelected = _selectedIndex == index;

              return Center(
                child: AneText(
                  currencyModel.name,
                  style: context.textTheme.regular.fs16.w500.copyWith(
                    color:
                        isSelected ? context.colorTheme.btnBgPrimary : context.colorTheme.textPrimary.withOpacity(0.6),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildOperateView() {
    return Row(
      children: [
        Expanded(
            child: CommonButton(
          title: "cancel".tr(),
          height: 42.gw,
          style: CommonButtonStyle.secondary,
          onPressed: () => sl<NavigatorService>().pop(),
        )),
        SizedBox(width: 12.gw),
        Expanded(
            child: CommonButton(
              title: "ok".tr(),
              height: 42.gw,
              onPressed: () {
                sl<NavigatorService>().pop();
                final currency = dataList[_selectedIndex];
                onClickCurrency(currency);
              },
            )),
      ],
    );
  }
}
