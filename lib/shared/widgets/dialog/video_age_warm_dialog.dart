import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class VideoAgeWarmDialog {
  final VoidCallback onTapConfirm;

  VideoAgeWarmDialog({
    required this.onTapConfirm,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _VideoAgeWarmDialogContent(
          onTapConfirm: onTapConfirm,
        );
      },
    );
  }
}

class _VideoAgeWarmDialogContent extends StatelessWidget {
  final VoidCallback onTapConfirm;

  const _VideoAgeWarmDialogContent({
    required this.onTapConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 362.gw,
          padding: EdgeInsets.all(24.gw),
          decoration: BoxDecoration(
            image: const DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AneText(
                'warm_title'.tr(),
                style: context.textTheme.secondary.fs24.w600,
              ),
              SizedBox(height: 57.gw),
              Image.asset("assets/images/video/icon_dialog_18+.png", width: 52.gw, height: 52.gw),
              SizedBox(height: 16.gw),
              AneText(
                'video_18_tips'.tr(),
                textAlign: TextAlign.center,
                style: context.textTheme.highlight.fs18,
              ),
              SizedBox(height: 43.gw),
              Row(
                children: [
                  Expanded(
                    child: CommonButton(
                      title: 'under_18'.tr(),
                      style: CommonButtonStyle.secondary,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  SizedBox(width: 12.gw),
                  Expanded(
                    child: CommonButton(
                      title: '18+',
                      onPressed: () {
                        Navigator.of(context).pop();
                        onTapConfirm();
                      },
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
