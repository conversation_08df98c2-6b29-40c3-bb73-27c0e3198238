import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/card/metric_card.dart';

class BalanceCard extends StatefulWidget {
  final String title;
  final Future<void> Function() onClickRefresh;

  const BalanceCard({
    super.key,
    required this.title,
    required this.onClickRefresh,
  });

  @override
  State<StatefulWidget> createState() => _BalanceCardState();
}

class _BalanceCardState extends State<BalanceCard> with SingleTickerProviderStateMixin {
  late AnimationController _balanceRefreshController;

  @override
  void initState() {
    _balanceRefreshController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
    super.initState();
  }

  @override
  void dispose() {
    _balanceRefreshController.dispose();
    super.dispose();
  }

  void _handleWalletRefresh() async {
    _balanceRefreshController.repeat();
    await widget.onClickRefresh.call();
    _balanceRefreshController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<UserCubit, UserState, String>(
      selector: (state) => state.balanceInfo?.accountMoney.formattedMoney ?? '-',
      builder: (context, balance) {
        return MetricCard(
          widget.title,
          iconPath: "assets/images/transact/v3/icon_balance.png",
          value: balance,
          suffixIcon: RotationTransition(
            turns: _balanceRefreshController.drive(
              CurveTween(curve: Curves.easeInOutCubic),
            ),
            child: Image.asset(
              "assets/images/mine/icon_mine_refresh.png",
              width: 24.gw,
              height: 24.gw,
            ),
          ),
          onTap: _handleWalletRefresh,
        );
      },
    );
  }
}
