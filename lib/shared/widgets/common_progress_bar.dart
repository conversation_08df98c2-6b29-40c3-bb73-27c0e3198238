import 'dart:math';

import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

class CommonProgressBar extends StatelessWidget {
  final int total;
  final double progress; // range 0 to 1
  final double? itemWidth;

  const CommonProgressBar({
    super.key,
    this.total = 10,
    required double progress,
    this.itemWidth,
  }) : progress = progress < 0 ? 0 : (progress > 1 ? 1 : progress);

  @override
  Widget build(BuildContext context) {
    final itemW = itemWidth ?? 23.84.gw;
    final maxTotal = min(total, 10);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        border: Border.all(
          color: context.colorTheme.btnBorderTertiary,
          width: 0.5,
        ),
      ),
      height: 13.gw,
      width: itemW * maxTotal,
      child: ListView.builder(
        itemCount: maxTotal,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          final isFirst = index == 0;
          final isLast = index == maxTotal - 1;
          final isNotCompleted = index >= (progress * maxTotal);
          return ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(isFirst ? 4 : 0),
              topRight: Radius.circular(isLast ? 4 : 0),
              bottomLeft: Radius.circular(isFirst ? 4 : 0),
              bottomRight: Radius.circular(isLast ? 4 : 0),
            ),
            child: AppImage(
              imageUrl:
                  isNotCompleted ? 'assets/images/mine/vip/vip_level_bg.png' : 'assets/images/mine/vip/vip_level.png',
              width: itemW,
            ),
          );
        },
      ),
    );
  }
}
