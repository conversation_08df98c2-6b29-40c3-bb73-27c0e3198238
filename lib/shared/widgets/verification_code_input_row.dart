import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class VerificationCodeInputRow extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String buttonText;
  final VoidCallback onButtonPressed;
  final String iconAsset;
  final ValueChanged<String>? onChanged;
  final bool isTimerActive;
  final int countdown;

  const VerificationCodeInputRow({
    super.key,
    required this.controller,
    required this.hintText,
    required this.buttonText,
    required this.onButtonPressed,
    required this.iconAsset,
    this.onChanged,
    this.isTimerActive = false,
    this.countdown = 0,
  });

  @override
  Widget build(BuildContext context) {
    final borderRadius = BorderRadius.circular(12.gw);
    return Container(
      height: 50.gw,
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: borderRadius,
        border: Border.all(
          color: context.colorTheme.borderA,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon background
          Container(
            width: 40.gw,
            height: double.infinity,
            decoration: BoxDecoration(
              // color: context.colorTheme.tabItemBgA,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.gw),
                bottomLeft: Radius.circular(12.gw),
              ),
            ),
            child: Center(
              child: Image.asset(
                iconAsset,
                width: 20.gw,
                height: 20.gw,
              ),
            ),
          ),
          SizedBox(width: 16.gw),
          // Input field
          Expanded(
            child: TextField(
              controller: controller,
              onChanged: onChanged,
              style: context.textTheme.primary.copyWith(
                fontSize: 16.gw,
                color: context.colorTheme.textPrimary,
              ),
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: context.textTheme.highlight,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 15.gw, horizontal: 18.gw),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: 8.gw),
            child: ElevatedButton(
              onPressed: isTimerActive ? null : onButtonPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    isTimerActive ? context.colorTheme.textSecondary.withOpacity(0.3) : context.colorTheme.tabItemBgA,
                foregroundColor: isTimerActive ? context.colorTheme.textSecondary : context.colorTheme.textPrimary,
                padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gw),
                minimumSize: Size(80.gw, 36.gw),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.gw),
                ),
                elevation: 0,
              ),
              child: Text(
                isTimerActive ? '${countdown}s' : buttonText,
                style: TextStyle(fontSize: 12.gw),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
