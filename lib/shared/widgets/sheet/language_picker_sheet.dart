import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/0_tiktok/video_library/popular_video/popular_video_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_library_cubit.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/gradient_border.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class LanguagePickerSheet {
  final BuildContext context;

  LanguagePickerSheet(this.context);

  show() {
    return CommonBottomSheet.show(
      context: context,
      maxHeight: 431.gw,
      gradientOverlayHeight: 100.0,
      isScrollControlled: false,
      contentPadding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 16.gw),
      title: 'language'.tr(),
      onTapSure: () {
        final selLocale = LocaleUtil().supportLocaleList[_selectedIndex];
        LocaleUtil().setCurrentLocale(selLocale);
        sl<ActivityListCubit>().reset(); /// 刷新活动语言
        sl<PopularVideoCubit>().reset(); /// 刷新视频相关语言
      },
      children: [
        _buildLocaleWheelPicker(),
      ],
    );
  }

  int _selectedIndex = 0;

  Widget _buildLocaleWheelPicker() {
    final currentLocale = LocaleUtil().currentLocale;
    final locales = LocaleUtil().supportLocaleList;
    _selectedIndex = locales.indexOf(currentLocale);
    return StatefulBuilder(
      builder: (context, setState) {
        return SizedBox(
          height: 200.gw,
          child: CupertinoPicker(
            scrollController: FixedExtentScrollController(initialItem: _selectedIndex),
            itemExtent: 45.gw,
            backgroundColor: Colors.transparent,
            selectionOverlay: Container(
              decoration: BoxDecoration(
                border: GradientBoxBorder(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.0),
                    ],
                  ),
                  width: 1.gw,
                ),
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                  ],
                ),
              ),
            ),
            onSelectedItemChanged: (int index) {
              _selectedIndex = index;
            },
            children: List.generate(locales.length, (index) {
              final locale = locales[index];
              final isSelected = LocaleUtil().currentLocale == locale;

              return Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(StringUtil.countryCodeToEmoji(locale.countryCode), style: TextStyle(fontSize: 16.fs)),
                    SizedBox(width: 8.gw),
                    AneText(
                      locale.name,
                      style: context.textTheme.regular.fs16.w500.copyWith(
                        color: isSelected
                            ? context.colorTheme.btnBgPrimary
                            : context.colorTheme.textPrimary.withOpacity(0.6),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
