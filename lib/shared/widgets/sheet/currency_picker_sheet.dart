import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/gradient_border.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class CurrencyPickerSheet {
  final BuildContext context;
  final CurrencyConfig currentCurrency;
  final List<CurrencyConfig> dataList;
  final void Function(CurrencyConfig) onClickCurrency;

  CurrencyPickerSheet(this.context, {required this.currentCurrency,required this.dataList, required this.onClickCurrency});

  show() {
    return CommonBottomSheet.show(
      context: context,
      maxHeight: 431.gw,
      gradientOverlayHeight: 100.0,
      isScrollControlled: false,
      contentPadding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 16.gw),
      title: 'select_currency'.tr(),
      onTapSure: () {
        final currency = dataList[_selectedIndex];
        onClickCurrency(currency);
      },
      children: [
        _buildContentWheelPicker(),
      ],
    );
  }

  int _selectedIndex = 0;

  Widget _buildContentWheelPicker() {
    _selectedIndex = dataList.indexOf(currentCurrency);
    return StatefulBuilder(
      builder: (context, setState) {
        return SizedBox(
          height: 200.gw,
          child: CupertinoPicker(
            scrollController: FixedExtentScrollController(initialItem: _selectedIndex),
            itemExtent: 45.gw,
            backgroundColor: Colors.transparent,
            selectionOverlay: Container(
              decoration: BoxDecoration(
                border: GradientBoxBorder(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.0),
                    ],
                  ),
                  width: 1.gw,
                ),
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                  ],
                ),
              ),
            ),
            onSelectedItemChanged: (int index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            children: List.generate(dataList.length, (index) {
              final currencyModel = dataList[index];
              final isSelected = _selectedIndex == index;

              return Center(
                child: AneText(
                  currencyModel.name,
                  style: context.textTheme.regular.fs16.w500.copyWith(
                    color: isSelected
                        ? context.colorTheme.btnBgPrimary
                        : context.colorTheme.textPrimary.withOpacity(0.6),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
