import 'package:flutter/material.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CountrySelectorField extends StatefulWidget {
  final Country? selectedCountry;
  final ValueChanged<Country>? onCountryChanged;
  final String hintText;
  final Widget? prefixIcon;
  final bool enabled;

  const CountrySelectorField({
    super.key,
    this.selectedCountry,
    this.onCountryChanged,
    this.hintText = 'Select Country',
    this.prefixIcon,
    this.enabled = true,
  });

  @override
  State<CountrySelectorField> createState() => _CountrySelectorFieldState();
}

class _CountrySelectorFieldState extends State<CountrySelectorField> {
  Country? _selectedCountry;
  List<Country> _countries = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedCountry = widget.selectedCountry;
    _loadCountries();
  }

  @override
  void didUpdateWidget(CountrySelectorField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedCountry != oldWidget.selectedCountry) {
      setState(() {
        _selectedCountry = widget.selectedCountry;
      });
    }
  }

  Future<void> _loadCountries() async {
    try {
      final countries = await CountryService.instance.getCountries();
      if (mounted) {
        setState(() {
          _countries = countries;
          _isLoading = false;
          // Set default country if none selected
          if (_selectedCountry == null && countries.isNotEmpty) {
            _selectedCountry = countries.first;
            widget.onCountryChanged?.call(_selectedCountry!);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showCountryPicker() {
    if (!widget.enabled || _countries.isEmpty) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCountryPickerSheet(),
    );
  }

  Widget _buildCountryPickerSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.gw),
          topRight: Radius.circular(20.gw),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 8.gw),
            width: 40.gw,
            height: 4.gw,
            decoration: BoxDecoration(
              color: context.colorTheme.textSecondary.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2.gw),
            ),
          ),
          
          // Title
          Padding(
            padding: EdgeInsets.all(16.gw),
            child: Text(
              'Select Country',
              style: context.textTheme.title.copyWith(
                fontSize: 18.gw,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Countries list
          Expanded(
            child: ListView.builder(
              itemCount: _countries.length,
              itemBuilder: (context, index) {
                final country = _countries[index];
                final isSelected = _selectedCountry?.code == country.code;
                
                return ListTile(
                  leading: Text(
                    country.flag,
                    style: TextStyle(fontSize: 24.gw),
                  ),
                  title: Text(
                    country.name,
                    style: context.textTheme.primary.copyWith(
                      fontSize: 16.gw,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  subtitle: Text(
                    '+${country.areaCode}',
                    style: context.textTheme.secondary.copyWith(
                      fontSize: 14.gw,
                    ),
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: context.theme.primaryColor,
                          size: 20.gw,
                        )
                      : null,
                  onTap: () {
                    setState(() {
                      _selectedCountry = country;
                    });
                    widget.onCountryChanged?.call(country);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 60.gw,
      child: GestureDetector(
        onTap: widget.enabled ? _showCountryPicker : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            border: Border.all(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Prefix icon container
              if (widget.prefixIcon != null)
                Container(
                  width: 56.gw,
                  height: 60.gw,
                  decoration: BoxDecoration(
                    color: context.colorTheme.iconBgA,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.gw),
                      bottomLeft: Radius.circular(12.gw),
                    ),
                  ),
                  child: widget.prefixIcon,
                ),
              
              if (widget.prefixIcon != null) SizedBox(width: 20.gw),
              
              // Content
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Row(
                    children: [
                      if (_isLoading)
                        SizedBox(
                          width: 16.gw,
                          height: 16.gw,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              context.colorTheme.textSecondary,
                            ),
                          ),
                        )
                      else if (_selectedCountry != null) ...[
                        Text(
                          _selectedCountry!.flag,
                          style: TextStyle(fontSize: 20.gw),
                        ),
                        SizedBox(width: 8.gw),
                        Expanded(
                          child: Text(
                            '${_selectedCountry!.name} (+${_selectedCountry!.areaCode})',
                            style: context.textTheme.regular.fs16,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ] else
                        Expanded(
                          child: Text(
                            widget.hintText,
                            style: context.textTheme.highlight,
                          ),
                        ),
                      
                      // Dropdown arrow
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: context.colorTheme.textSecondary,
                        size: 20.gw,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
