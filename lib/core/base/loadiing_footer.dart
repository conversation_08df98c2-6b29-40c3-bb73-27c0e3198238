import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class LoadingFooter extends StatelessWidget {
  const LoadingFooter({super.key});

  ///logo
  Widget _logo() {
    return Container();
  }

  ///提示语
  ///
  /// [showText] 提示内容
  Widget _prompt(String showText, {bool isShowIndicator = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isShowIndicator) ...[
          SizedBox(width: 15.gw, height: 15.gw, child: const CircularProgressIndicator()),
          SizedBox(width: 10.gw),
        ],
        Text(
          showText,
          style: TextStyle(
            color: const Color(0xFF979797),
            fontSize: 14.fs,
          ),
        ),
      ],
    );
  }

  ///提示语和log
  ///
  /// [showText] 提示内容
  Widget _hint(String showText, {bool isShowIndicator = false}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.gw),
      child: Column(
        children: [_prompt(showText), SizedBox(height: 4.gw), _logo()],
      ),
    );
  }

  ///列表头
  CustomFooter _footer() {
    Widget body = _hint("pull_up_to_load_more".tr());
    return CustomFooter(
      builder: (context, mode) {
        if (mode == LoadStatus.idle) {
          body = _hint("pull_up_to_load_more".tr());
        } else if (mode == LoadStatus.loading) {
          body = _hint("loading".tr(), isShowIndicator: true);
        } else if (mode == LoadStatus.failed) {
          body = _hint("load_failed".tr());
        } else if (mode == LoadStatus.canLoading) {
          body = _hint("release_to_load_more".tr());
        } else {
          body = _hint("no_more_data".tr());
        }
        return body;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return _footer();
  }
}
