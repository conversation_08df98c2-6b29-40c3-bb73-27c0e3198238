import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/utils/screenUtil.dart';

class RefreshHeader extends StatelessWidget {
  const RefreshHeader({super.key});

  ///提示语
  ///
  /// [showText] 提示内容
  Widget _prompt(String showText, {bool isShowIndicator = false}) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isShowIndicator) ...[
              SizedBox(width: 15.gw, height: 15.gw, child: CircularProgressIndicator(strokeWidth: 1.gw,)),
              SizedBox(width: 10.gw),
            ],
            Text(
              showText,
              style: TextStyle(color: const Color(0xFF979797), fontSize: 14.fs),
            ),
          ],
        ),
        SizedBox(height: 10.gw),
      ],
    );
  }

  ///提示语和log
  ///
  /// [showText] 提示内容
  Widget _hint(String showText, {bool isShowIndicator = false}) {
    return Column(
      children: [
        _prompt(showText, isShowIndicator: isShowIndicator),
        SizedBox(height: 8.gw) /*, _prompt(showText)*/
      ],
    );
  }

  ///列表头
  CustomHeader _header() {
    Widget body = _hint("pull_to_refresh_hint".tr()); // 下拉刷新
    return CustomHeader(
      builder: (context, mode) {
        if (mode == RefreshStatus.idle) {
          body = _hint("pull_to_refresh_hint".tr()); // 下拉刷新
        } else if (mode == RefreshStatus.refreshing) {
          body = _hint("refreshing".tr(), isShowIndicator: true); // 正在刷新
        } else if (mode == RefreshStatus.failed) {
          body = _hint("load_failed".tr()); // 加载失败!
        } else if (mode == RefreshStatus.canRefresh) {
          body = _hint("release_to_refresh".tr()); // 松手进行刷新
        } else if (mode == RefreshStatus.completed) {
          body = _hint("update_complete".tr()); // 已为您更新最新数据
        } else {
          body = _hint("load_failed".tr()); // 加载失败!
        }
        return body;
      },
    );
  }

  @override
  Widget build(BuildContext context) => _header();
}
