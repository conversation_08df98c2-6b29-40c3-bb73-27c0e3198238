import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:wd/core/base/time_out_widget.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_app_bar.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'base_state.dart';
import 'base_will_pop.dart';
import 'empty_widget.dart';
import 'net_404_widget.dart';
import 'net_error_widget.dart';

typedef BodyBuilder = Widget Function(BaseState baseState, BuildContext context);

abstract class BasePage extends StatefulWidget {
  const BasePage({super.key});

  @override
  BasePageState createState() => getState();

  ///子类实现
  BasePageState getState();
}

abstract class BasePageState<T extends BasePage> extends State<T> {
  /// 是否渲染buildPage内容
  bool _isRenderPage = false;

  /// 是否渲染导航栏
  bool isRenderHeader = true;

  /// 导航栏颜色
  Color? navColor;

  /// 左右按钮横向padding
  final EdgeInsets _btnPaddingH = EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw);

  /// 导航栏高度
  double navBarH = AppBar().preferredSize.height;

  /// 顶部状态栏高度
  double statusBarH = 0.0;

  /// 底部安全区域高度
  double bottomSafeBarH = 0.0;

  /// 页面背景色
  Color? pageBgColor;

  /// header显示页面title
  String pageTitle = '';

  /// header显示页面title颜色
  Color? pageTitleColor;

  /// 是否允许某个页iOS滑动返回，Android物理返回键返回
  bool isAllowBack = true;

  bool resizeToAvoidBottomInset = true;

  /// 是否展示空数据页
  bool isNeedEmptyDataWidget = true;

  /// 是否允许点击返回上一页
  bool isBack = true;

  @override
  void initState() {
    super.initState();
    _getBarInfo();
    _addFirstFrameListener();
  }

  @override
  void dispose() {
    GSEasyLoading.dismiss();
    super.dispose();
  }

  void _addFirstFrameListener() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      buildComplete();
    });
  }

  void buildComplete() {}

  /// 获取屏幕状态栏和顶部导航栏的高度
  void _getBarInfo() {
    WidgetsBinding.instance.addPostFrameCallback((mag) {
      statusBarH = ScreenUtil().statusBarHeight;
      bottomSafeBarH = ScreenUtil().bottomBarHeight;
      // if (SystemUtil.isIOS() && ScreenUtil().bottomBarHeight > 0) {
      //   bottomSafeBarH = 14.gw;
      // }
      setState(() {
        _isRenderPage = true;
      });
    });
  }

  /// 点击左边按钮
  void onTapLeft() {
    if (!isBack) return;
    sl<NavigatorService>().unFocus();
    sl<NavigatorService>().pop();
  }

  ///抽象header上的组件
  Widget left() {
    return Image(
      image: const AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
      height: 20.gw,
      width: 20.gw,
    );
  }

  Widget right() => SizedBox(width: 20.gw);

  Widget _left() {
    if (!isBack) return const SizedBox.shrink();
    return InkWell(
      onTap: onTapLeft,
      child: Container(
        padding: _btnPaddingH,
        child: left(),
      ),
    );
  }

  /// 右边组件
  Widget _right() {
    return Container(
      padding: _btnPaddingH,
      child: right(),
    );
  }

  /// 右边抽屉
  Widget? buildEndDrawer() {
    return null;
  }

  /// 子类实现，构建各自页面UI控件
  Widget buildPage(BuildContext context);

  Widget _content() {
    return Container(
      color: pageBgColor ?? context.theme.scaffoldBackgroundColor,
      height: 1.sh,
      width: 1.sw,
      child: buildPage(context),
    );
  }

  /// 空数据页面
  Widget emptyWidget({String? title}) {
    return EmptyWidget(
      title: title,
    );
  }

  /// 加载出错页面
  Widget errorWidget(String title, Function refreshMethod) {
    return NetErrorWidget(title: title, refreshMethod: refreshMethod);
  }

  /// 接口404
  Widget net404Widget(String title) {
    return Net404Widget(
      title: title,
    );
  }

  /// 接口超时
  Widget timeOutWidget(String title, Function refreshMethod) {
    return TimeOutWidget(title: title, refreshMethod: refreshMethod);
  }

  Widget resultWidget(BaseState state, BodyBuilder builder, {Function? refreshMethod, Widget? loadingWidget}) {
    if (state.netState == NetState.loading) {
      return loadingWidget ?? const SizedBox();
    } else if (state.netState == NetState.empty && isNeedEmptyDataWidget) {
      return emptyWidget(title:  'no_data'.tr());
    } else if (state.netState == NetState.showReload) {
      return errorWidget('network_error'.tr(), refreshMethod ?? () {});
    } else if (state.netState == NetState.failed) {
      return net404Widget('404');
    } else if (state.netState == NetState.idle) {
      return emptyWidget(title: 'initialize State');
    } else if (state.netState == NetState.timeOut) {
      return timeOutWidget('timeout_retry'.tr(), refreshMethod ?? () {});
    } else {
      return builder(state, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardDismisser(
      child: BaseWillPopPage(
        isAllowBack: isAllowBack,
        child: Scaffold(
          appBar: isRenderHeader == true
              ? CommonAppBar(
                  navColor: navColor,
                  pageTitle: pageTitle,
                  pageTitleColor: pageTitleColor,
                  leftWidget: _left(),
                  rightWidget: _right(),
                )
              : null,
          endDrawer: buildEndDrawer(),
          body: _isRenderPage == false ? const SizedBox() : _content(),
          resizeToAvoidBottomInset: false,
        ),
      ),
    );
  }
}
