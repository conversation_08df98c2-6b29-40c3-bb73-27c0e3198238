/// BaseState
/// 项目中所有需要根据网络状态显示页面的state必须继承于BaseState

enum NetState {
  /// 初始状态
  idle,

  /// 加载状态
  loading,

  /// 错误状态,显示失败界面
  failed,

  /// 错误状态,显示刷新按钮
  showReload,

  /// 空数据状态
  empty,

  /// 加载超时
  timeOut,

  /// 数据获取成功状态
  success,
}

abstract class BaseState<T> {
  /// 页面状态
  final NetState netState;

  /// 是否还有更多数据
  final bool? isNoMoreDataState;

  /// 数据是否请求完成
  final bool? isNetWorkFinish;

  /// 数据源
  final List<T>? dataList;

  /// 网络加载次数 用这个属性判断 BlocConsumer 是否需要监听刷新数据
  final int netLoadCount;

  const BaseState({
    this.netState = NetState.idle,
    this.isNoMoreDataState = false,
    this.isNetWorkFinish = false,
    this.dataList = const [],
    this.netLoadCount = 0,
  });
}
