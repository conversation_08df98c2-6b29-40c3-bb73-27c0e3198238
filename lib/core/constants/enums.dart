import 'package:flutter/material.dart';

enum DataStatus {
  idle,
  loading,
  success,
  failed,
}
 enum DatePickerType {
  year,
  month,
  day,
}
enum ApiMethod {
  get,
  post,
  patch,
}

/// 注单日期类型
enum RecordDateType {
  today('NOW'),
  yesterday('YESTERDAY'),
  week('7DAY');

  const RecordDateType(this.typeName);

  final String typeName;
}

/// 游戏类型
enum ChannelType {
  SX('真人', 'assets/images/channel/icon_channel_SX.png', Color(0xFFd24cb7)),
  DZ('电子', 'assets/images/channel/icon_channel_DZ.png', Color(0xFF886adf)),
  DJ('电竞', 'assets/images/channel/icon_channel_DZ.png', Color(0xFF886adf)),
  BY('捕鱼', 'assets/images/channel/icon_channel_BY.png', Color(0xFF6cb3cf)),
  QP('棋牌', 'assets/images/channel/icon_channel_QP.png', Color(0xFF578941)),
  TY('体育', 'assets/images/channel/icon_channel_TY.png', Color(0xFF887849)),
  CP('彩票', 'assets/images/channel/icon_channel_CP.png', Color(0xFFd45175)),
  ALL('娱乐场', 'assets/images/channel/icon_channel_ALL.png', Color(0xFF7b583f)),
  HOT('热门', 'assets/images/channel/icon_channel_HOT.png', Color(0xFF9b5b5c)),
  JIN('18禁', 'assets/images/channel/icon_channel_JIN.png', Color(0xFFa1353d));

  const ChannelType(this.channelName, this.channelLogo, this.shadowColor);

  final String channelName;
  final String channelLogo;
  final Color shadowColor;

  /// 获取游戏类型图标
  static String getLogoByCode(String code, {bool isSel = true}) {
    for (ChannelType type in ChannelType.values) {
      if (type.name == code) {
        return isSel ? type.channelLogo.replaceAll(".png", "_sel.png") : type.channelLogo;
      }
    }
    return 'unknown';
  }

  static Color getShadowColorByCode(String code) {
    for (ChannelType type in ChannelType.values) {
      if (type.name == code) {
        return type.shadowColor;
      }
    }
    return Colors.transparent;
  }
}

final List<String> genders = ['male', 'female', 'other'];

enum UserFieldType { account, realName, nickName, phone, email }

/// 活动彩金领取状态
enum ActivityRewardStatus {
  unknown, // -1: 未知
  notReceived, // 0: 未领取
  received, // 1: 已领取
  expired // 2: 已过期
}

extension ActivityRewardStatusExtension on ActivityRewardStatus {
  static ActivityRewardStatus fromValue(int value) {
    switch (value) {
      case 0:
        return ActivityRewardStatus.notReceived;
      case 1:
        return ActivityRewardStatus.received;
      case 2:
        return ActivityRewardStatus.expired;
      default:
        return ActivityRewardStatus.unknown; // 默认返回未知状态
    }
  }
}

/// 登录方式: 1手机号 2账户密码 3邮箱 4google 5facebook 6tiktok
enum LoginType {
  phone(1),
  userName(2),
  email(3),
  google(4),
  facebook(5),
  tiktok(6);

  final int code;

  const LoginType(this.code);
}

/// Authentication modes available in the application
enum AuthType {
  login,
  register,
}

/// 登录注册配置项
enum AuthMethodType {
  phoneOnly, // 仅手机号
  accountOnly, // 仅账号
  both; // 两者都支持

  static AuthMethodType fromConfig(String? config) {
    switch (config) {
      case '1':
        return AuthMethodType.phoneOnly;
      case '2':
        return AuthMethodType.accountOnly;
      case '1,2':
      case '2,1':
        return AuthMethodType.both;
      default:
        return AuthMethodType.both;
    }
  }

  bool get supportsPhone => this == AuthMethodType.phoneOnly || this == AuthMethodType.both;

  bool get supportsAccount => this == AuthMethodType.accountOnly || this == AuthMethodType.both;
}
