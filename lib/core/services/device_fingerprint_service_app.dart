import 'dart:io';
import 'dart:math';
import 'dart:convert';
import 'dart:ui' as ui;
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/device_fingerprint.dart';

/// Service for collecting device fingerprint information
class DeviceFingerprintService {
  static final DeviceFingerprintService _instance = DeviceFingerprintService._internal();
  factory DeviceFingerprintService() => _instance;
  DeviceFingerprintService._internal();

  static const String _fingerprintVersion = '1.0.0';

  /// Generate complete device fingerprint
  Future<DeviceFingerprint> generateFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();
    final connectivity = Connectivity();

    // Get platform-specific device information
    int platform = 5; // Default: Other
    int deviceType = 1; // Default: Phone
    String brand = 'Unknown';
    String model = 'Unknown';
    String os = 'Unknown';
    String osVer = 'Unknown';
    String osBuild = 'Unknown';
    String osArch = 'Unknown';
    String cpu = 'Unknown';
    int memoryGb = 0;

    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      platform = 2; // iOS - 与 SystemUtil.getPlatformCode() 保持一致
      deviceType = _getIOSDeviceType(iosInfo.model);
      brand = 'Apple';
      model = iosInfo.model;
      os = 'iOS';
      osVer = iosInfo.systemVersion;
      osBuild = iosInfo.systemVersion;
      osArch = _getIOSArchitecture(iosInfo.model);
      cpu = _getIOSCPU(iosInfo.model);
      memoryGb = _getIOSMemory(iosInfo.model);
    } else if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      platform = 1; // Android - 与 SystemUtil.getPlatformCode() 保持一致
      deviceType = _getAndroidDeviceType(androidInfo.model);
      brand = androidInfo.brand;
      model = androidInfo.model;
      os = 'Android';
      osVer = androidInfo.version.release;
      osBuild = androidInfo.version.incremental;
      osArch = _getAndroidArchitecture(androidInfo);
      cpu = androidInfo.hardware;
      memoryGb = _estimateAndroidMemory(androidInfo);
    }

    // Get screen information using MediaQuery fallback
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final screenW = screenSize.width.toInt();
    final screenH = screenSize.height.toInt();

    // Get network information
    final connectivityResults = await connectivity.checkConnectivity();
    final network = await _getNetworkType(connectivityResults.isNotEmpty ? connectivityResults.first : ConnectivityResult.none);

    // Get locale and timezone
    final locale = ui.PlatformDispatcher.instance.locale;
    final lang = '${locale.languageCode}-${locale.countryCode}';
    final timezone = DateTime.now().timeZoneName;

    // Generate fingerprint hashes
    final canvasFp = await _generateCanvasFingerprint();
    final webglFp = await _generateWebGLFingerprint();
    final audioFp = await _generateAudioFingerprint();
    final behaviorFp = await _generateBehaviorFingerprint();

    // Detect security features
    final isRooted = await _detectRootJailbreak();
    final isEmulator = await _detectEmulator();
    const isDebug = kDebugMode ? 1 : 2;
    final isProxy = await _detectProxy();

    return DeviceFingerprint(
      // 生成 device_uuid 所用的指纹算法版本
      fingerprintVer: _fingerprintVersion,
      // 平台类型（1.iOS 2.Android 3.H5 4.桌面应用 5.其他）
      platform: platform,
      // 设备类型（1.手机 2.平板 3.PC 4.智能电视 5.可穿戴 6.其他）
      deviceType: deviceType,
      // 设备品牌（例如: Apple, Samsung, Huawei...）
      brand: brand,
      // 设备型号（例如: iPhone 15 Pro, Pixel 8, Dell XPS 15...）
      model: model,
      // 应用/客户端版本号
      appVer: packageInfo.version,
      // 操作系统类型（例如: iOS, Android, Windows, macOS, Linux...）
      os: os,
      // 操作系统版本号（如 17.5.1, 14.0.0, 10, 14.5）
      osVer: osVer,
      // 操作系统构建号/内部版本号
      osBuild: osBuild,
      // 操作系统架构（如 arm64, x86_64）
      osArch: osArch,
      // 系统语言（Accept-Language，如 en-US, zh-CN）
      lang: lang,
      // 时区（如 Asia/Shanghai, America/New_York）
      timezone: timezone,
      // 屏幕宽度（像素）
      screenW: screenW,
      // 屏幕高度（像素）
      screenH: screenH,
      // 屏幕颜色深度（位数，如 24, 32）
      colorDepth: 32, // Standard for mobile devices
      // 屏幕方向
      orientation: screenW > screenH ? 'landscape' : 'portrait',
      // CPU信息（型号名称，如 Apple A17 Pro, Snapdragon 8 Gen 3, Intel Core i7-13700K）
      cpu: cpu,
      // GPU/显卡信息（厂商和渲染器名称，如 Apple GPU, Adreno 750, NVIDIA GeForce RTX 4080）
      gpu: await _getGPUInfo(),
      // 内存大小（GB）
      memoryGb: memoryGb,
      // 电池信息（如电量、充电状态）
      battery: await _getBatteryInfo(),
      // 传感器支持情况（如陀螺仪、加速度计等，JSON数组）
      sensor: await _getSensorInfo(),
      // 客户端公网IP地址
      ip: await _getPublicIP(),
      // 网络类型（如 WiFi, 4G, 5G, Ethernet, Unknown）
      network: network,
      // 移动网络运营商名称（仅移动端）
      carrier: await _getCarrierInfo(),
      // User-Agent 字符串
      userAgent: await _getUserAgent(),
      // 已安装字体列表（JSON数组格式）
      fonts: await _getSystemFonts(),
      // 浏览器插件列表（JSON数组格式，仅H5）
      plugins: [], // Not applicable for mobile apps
      // Canvas 渲染指纹哈希（SHA256或更长）
      canvasFp: canvasFp,
      // WebGL 渲染指纹哈希
      webglFp: webglFp,
      // AudioContext 指纹哈希
      audioFp: audioFp,
      // 浏览器存储能力支持情况（如localStorage, indexedDB, Web SQL）
      storage: _getStorageCapabilities(),
      // 浏览器/系统新特性支持情况（如WebAssembly、ServiceWorker等，JSON数组）
      features: await _getSystemFeatures(),
      // 是否已Root/越狱（1.是，2.否）
      isRooted: isRooted,
      // 是否为模拟器/虚拟机（1.是，2.否）
      isEmulator: isEmulator,
      // 应用是否处于调试模式（1.是，2.否）
      isDebug: isDebug,
      // 是否检测到代理或VPN（1.是，2.否）
      isProxy: isProxy,
      // Android Play Integrity 验证结果（如 VERIFIED, BASIC_INTEGRITY_FAIL, DEVICE_INTEGRITY_FAIL）
      attestAndroid: Platform.isAndroid ? await _getAndroidAttestation() : '',
      // iOS DeviceCheck/App Attest 验证结果（如 VERIFIED, FRAUDULENT, NOT_SUPPORTED）
      attestIos: Platform.isIOS ? await _getIOSAttestation() : '',
      // 行为特征指纹（如鼠标/触摸/输入等行为哈希）
      behaviorFp: behaviorFp,
    );
  }

  /// 获取iOS设备类型
  /// 根据设备型号判断设备类型（1.手机 2.平板 6.其他）
  /// [model] iOS设备型号
  /// 返回设备类型代码
  int _getIOSDeviceType(String model) {
    final modelLower = model.toLowerCase();
    if (modelLower.contains('ipad')) return 2; // Tablet
    if (modelLower.contains('ipod')) return 6; // Other (iPod)
    return 1; // Phone
  }

  /// 获取Android设备类型
  /// 优先通过屏幕尺寸计算对角线长度判断设备类型，回退到模型名称判断
  /// [model] Android设备型号
  /// 返回设备类型代码（1.手机 2.平板 6.其他）
  int _getAndroidDeviceType(String model) {
    final modelLower = model.toLowerCase();
    
    // 通过屏幕尺寸判断设备类型
    try {
      final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
      final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
      
      // 计算屏幕对角线尺寸（英寸）
      final widthInches = screenSize.width / (devicePixelRatio * 160);
      final heightInches = screenSize.height / (devicePixelRatio * 160);
      final diagonalInches = sqrt((widthInches * widthInches + heightInches * heightInches));
      
      if (diagonalInches >= 7.0) return 2; // Tablet
      if (diagonalInches >= 3.0) return 1; // Phone
      return 6; // Other
    } catch (e) {
      // 如果获取屏幕信息失败，回退到模型名称判断
    }
    
    // 通过模型名称判断
    if (modelLower.contains('tablet') || 
        modelLower.contains('tab') || 
        modelLower.contains('pad')) return 2; // Tablet
    return 1; // Phone
  }

  /// 获取iOS设备架构
  /// 根据设备型号判断系统架构（arm64, armv7）
  /// [model] iOS设备型号
  /// 返回架构字符串
  String _getIOSArchitecture(String model) {
    // 根据设备型号判断架构
    final modelLower = model.toLowerCase();
    if (modelLower.contains('iphone') || modelLower.contains('ipad')) {
      // iPhone 5s 及以后的设备使用 arm64
      if (modelLower.contains('iphone5') || modelLower.contains('ipad2') || modelLower.contains('ipad3')) {
        return 'armv7'; // 32位设备
      }
      return 'arm64'; // 64位设备
    }
    return 'arm64'; // 默认
  }

  /// 获取Android设备架构
  /// 从Android设备信息中获取支持的架构（64位优先）
  /// [androidInfo] Android设备信息对象
  /// 返回架构字符串
  String _getAndroidArchitecture(dynamic androidInfo) {
    try {
      if (androidInfo.supported64BitAbis.isNotEmpty) {
        return androidInfo.supported64BitAbis.first;
      } else if (androidInfo.supported32BitAbis.isNotEmpty) {
        return androidInfo.supported32BitAbis.first;
      }
    } catch (e) {
      // 忽略错误
    }
    return 'unknown';
  }

  /// 获取iOS设备CPU信息
  /// 根据设备型号判断CPU型号（Apple A-Series, M-Series）
  /// [model] iOS设备型号
  /// 返回CPU型号字符串
  String _getIOSCPU(String model) {
    // 更准确的 iOS CPU 检测
    final modelLower = model.toLowerCase();
    if (modelLower.contains('iphone15') || modelLower.contains('iphone 15')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 'Apple A17 Pro';
      return 'Apple A16 Bionic';
    }
    if (modelLower.contains('iphone14') || modelLower.contains('iphone 14')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 'Apple A16 Bionic';
      return 'Apple A15 Bionic';
    }
    if (modelLower.contains('iphone13') || modelLower.contains('iphone 13')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 'Apple A15 Bionic';
      return 'Apple A15 Bionic';
    }
    if (modelLower.contains('iphone12') || modelLower.contains('iphone 12')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 'Apple A14 Bionic';
      return 'Apple A14 Bionic';
    }
    if (modelLower.contains('iphone11') || modelLower.contains('iphone 11')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 'Apple A13 Bionic';
      return 'Apple A13 Bionic';
    }
    if (modelLower.contains('iphonex') || modelLower.contains('iphone x')) return 'Apple A11 Bionic';
    if (modelLower.contains('iphone8') || modelLower.contains('iphone 8')) return 'Apple A11 Bionic';
    if (modelLower.contains('iphone7') || modelLower.contains('iphone 7')) return 'Apple A10 Fusion';
    if (modelLower.contains('iphone6') || modelLower.contains('iphone 6')) return 'Apple A8';
    if (modelLower.contains('iphone5') || modelLower.contains('iphone 5')) return 'Apple A6';
    
    // iPad 系列
    if (modelLower.contains('ipad')) {
      if (modelLower.contains('pro')) return 'Apple M-Series';
      if (modelLower.contains('air')) return 'Apple A-Series';
      return 'Apple A-Series';
    }
    
    return 'Apple A-Series';
  }

  /// 获取iOS设备内存大小
  /// 根据设备型号估算内存大小（GB）
  /// [model] iOS设备型号
  /// 返回内存大小（GB）
  int _getIOSMemory(String model) {
    // 更准确的 iOS 内存估算
    final modelLower = model.toLowerCase();
    
    // iPhone 系列
    if (modelLower.contains('iphone15') || modelLower.contains('iphone 15')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 8;
      return 6;
    }
    if (modelLower.contains('iphone14') || modelLower.contains('iphone 14')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 6;
      return 6;
    }
    if (modelLower.contains('iphone13') || modelLower.contains('iphone 13')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 6;
      return 4;
    }
    if (modelLower.contains('iphone12') || modelLower.contains('iphone 12')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 6;
      return 4;
    }
    if (modelLower.contains('iphone11') || modelLower.contains('iphone 11')) {
      if (modelLower.contains('pro') || modelLower.contains('max')) return 4;
      return 4;
    }
    if (modelLower.contains('iphonex') || modelLower.contains('iphone x')) return 3;
    if (modelLower.contains('iphone8') || modelLower.contains('iphone 8')) return 2;
    if (modelLower.contains('iphone7') || modelLower.contains('iphone 7')) return 2;
    if (modelLower.contains('iphone6') || modelLower.contains('iphone 6')) return 1;
    if (modelLower.contains('iphone5') || modelLower.contains('iphone 5')) return 1;
    
    // iPad 系列
    if (modelLower.contains('ipad')) {
      if (modelLower.contains('pro')) return 8;
      if (modelLower.contains('air')) return 4;
      return 2;
    }
    
    return 4; // 默认值
  }

  /// 估算Android设备内存大小
  /// 根据设备型号估算内存大小（GB）
  /// [androidInfo] Android设备信息对象
  /// 返回内存大小（GB）
  int _estimateAndroidMemory(dynamic androidInfo) {
    try {
      // 通过设备型号估算内存
      final model = androidInfo.model.toLowerCase();
      
      // 高端设备
      if (model.contains('s23') || model.contains('s24') || 
          model.contains('pixel 8') || model.contains('pixel 9') ||
          model.contains('oneplus') || model.contains('find x')) {
        return 8;
      }
      
      // 中端设备
      if (model.contains('a5') || model.contains('a7') || 
          model.contains('redmi') || model.contains('note')) {
        return 6;
      }
      
      // 低端设备
      if (model.contains('a1') || model.contains('a3') || 
          model.contains('galaxy j') || model.contains('galaxy m')) {
        return 4;
      }
      
    } catch (e) {
      // 忽略错误
    }
    
    return 4; // 默认值
  }

  /// 获取网络类型
  /// 根据连接结果判断网络类型（WiFi, 4G, Ethernet等）
  /// [result] 连接结果
  /// 返回网络类型字符串
  Future<String> _getNetworkType(ConnectivityResult result) async {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        // 尝试获取更详细的移动网络类型
        try {
          if (Platform.isAndroid) {
            // 在 Android 上可以尝试获取更详细的网络类型
            return '4G'; // 简化处理，实际应该通过原生代码获取
          } else if (Platform.isIOS) {
            return '4G'; // iOS 上也需要原生代码获取
          }
        } catch (e) {
          // 忽略错误
        }
        return '4G'; // 默认
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      default:
        return 'Unknown';
    }
  }

  /// 生成Canvas渲染指纹
  /// 基于设备特征生成Canvas指纹哈希（SHA256）
  /// 返回Canvas指纹哈希字符串
  Future<String> _generateCanvasFingerprint() async {
    // 生成基于设备特征的 Canvas 指纹
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
    
    // 使用屏幕尺寸和设备像素比生成指纹
    final data = utf8.encode('${screenSize.width}x${screenSize.height}x$devicePixelRatio');
    return sha256.convert(data).toString();
  }

  /// 生成WebGL渲染指纹
  /// 基于设备特征生成WebGL指纹哈希（SHA256）
  /// 返回WebGL指纹哈希字符串
  Future<String> _generateWebGLFingerprint() async {
    // 生成基于设备特征的 WebGL 指纹
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
    
    // 使用屏幕尺寸和设备像素比生成指纹
    final data = utf8.encode('webgl_${screenSize.width}x${screenSize.height}x$devicePixelRatio');
    return sha256.convert(data).toString();
  }

  /// 生成音频指纹
  /// 基于设备特征生成音频指纹哈希（SHA256）
  /// 返回音频指纹哈希字符串
  Future<String> _generateAudioFingerprint() async {
    // 生成基于设备特征的音频指纹
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    
    // 使用屏幕尺寸生成音频指纹
    final data = utf8.encode('audio_${screenSize.width}x${screenSize.height}');
    return sha256.convert(data).toString();
  }

  /// 生成行为特征指纹
  /// 基于设备特征生成行为指纹哈希（SHA256）
  /// 返回行为指纹哈希字符串
  Future<String> _generateBehaviorFingerprint() async {
    // 生成基于设备特征的行为指纹
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
    final locale = ui.PlatformDispatcher.instance.locale;
    
    // 使用多个设备特征生成行为指纹
    final data = utf8.encode('behavior_${screenSize.width}x${screenSize.height}x${devicePixelRatio}_$locale');
    return sha256.convert(data).toString();
  }

  /// 检测设备是否已Root/越狱
  /// 检查常见的Root/越狱指示器
  /// 返回检测结果（1.是，2.否）
  Future<int> _detectRootJailbreak() async {
    try {
      if (Platform.isAndroid) {
        // Check for common root indicators
        final rootPaths = ['/system/app/Superuser.apk', '/sbin/su', '/system/bin/su', '/system/xbin/su'];
        for (final path in rootPaths) {
          if (await File(path).exists()) return 1; // Rooted
        }
      } else if (Platform.isIOS) {
        // Check for common jailbreak indicators
        final jailbreakPaths = ['/Applications/Cydia.app', '/var/lib/cydia', '/etc/apt'];
        for (final path in jailbreakPaths) {
          if (await Directory(path).exists()) return 1; // Jailbroken
        }
      }
      return 2; // Not rooted/jailbroken
    } catch (e) {
      return 2; // Assume not rooted if detection fails
    }
  }

  /// 检测设备是否为模拟器/虚拟机
  /// 检查常见的模拟器指示器
  /// 返回检测结果（1.是，2.否）
  Future<int> _detectEmulator() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;

        // Check for common emulator indicators
        final emulatorIndicators = ['google_sdk', 'Emulator', 'Android SDK built for x86'];
        if (emulatorIndicators
            .any((indicator) => androidInfo.model.contains(indicator) || androidInfo.product.contains(indicator))) {
          return 1; // Emulator
        }
      }
      return 2; // Not emulator
    } catch (e) {
      return 2; // Assume not emulator if detection fails
    }
  }

  /// 检测是否使用代理或VPN
  /// 简化代理检测（默认返回未使用代理）
  /// 返回检测结果（1.是，2.否）
  Future<int> _detectProxy() async {
    // Simplified proxy detection
    return 2; // Not using proxy (default)
  }

  /// 获取GPU/显卡信息
  /// 获取设备GPU信息（简化实现）
  /// 返回GPU信息字符串
  Future<String> _getGPUInfo() async {
    // GPU information is not easily accessible in Flutter
    if (Platform.isIOS) return 'Apple GPU';
    if (Platform.isAndroid) return 'Adreno/Mali/PowerVR';
    return 'Unknown';
  }

  /// 获取电池信息
  /// 获取设备电池信息（需要原生代码实现）
  /// 返回电池信息字符串
  Future<String> _getBatteryInfo() async {
    // Battery information requires platform-specific implementation
    return 'Unknown';
  }

  /// 获取传感器信息
  /// 获取设备支持的传感器列表（需要原生代码实现）
  /// 返回传感器列表
  Future<List<String>> _getSensorInfo() async {
    // Sensor information requires platform-specific implementation
    return ['accelerometer', 'gyroscope', 'magnetometer'];
  }

  /// 获取公网IP地址
  /// 获取客户端公网IP地址（需要网络请求实现）
  /// 返回IP地址字符串
  Future<String> _getPublicIP() async {
    // Public IP detection requires network call
    return '0.0.0.0';
  }

  /// 获取运营商信息
  /// 获取移动网络运营商名称（需要原生代码实现）
  /// 返回运营商信息字符串
  Future<String> _getCarrierInfo() async {
    // Carrier information requires platform-specific implementation
    return 'Unknown';
  }

  /// 获取User-Agent字符串
  /// 生成应用User-Agent字符串
  /// 返回User-Agent字符串
  Future<String> _getUserAgent() async {
    // Generate a user agent string
    final packageInfo = await PackageInfo.fromPlatform();
    return 'WD/${packageInfo.version} (${Platform.operatingSystem})';
  }

  /// 获取系统字体列表
  /// 获取已安装的系统字体列表（简化实现）
  /// 返回字体列表
  Future<List<String>> _getSystemFonts() async {
    // System fonts are not easily accessible in Flutter
    return ['System Font'];
  }

  /// 获取存储能力支持情况
  /// 获取浏览器/应用存储能力支持情况
  /// 返回存储能力列表
  List<String> _getStorageCapabilities() {
    return ['SharedPreferences', 'SQLite', 'File System'];
  }

  /// 获取系统特性支持情况
  /// 获取浏览器/系统新特性支持情况
  /// 返回特性支持列表
  Future<List<String>> _getSystemFeatures() async {
    return ['Camera', 'GPS', 'Bluetooth', 'NFC'];
  }

  /// 获取Android设备认证结果
  /// 获取Android Play Integrity验证结果（需要Google Play Services集成）
  /// 返回认证结果字符串
  Future<String> _getAndroidAttestation() async {
    // Android attestation requires Google Play Services integration
    return 'NOT_SUPPORTED';
  }

  /// 获取iOS设备认证结果
  /// 获取iOS DeviceCheck/App Attest验证结果（需要集成相应服务）
  /// 返回认证结果字符串
  Future<String> _getIOSAttestation() async {
    // iOS attestation requires DeviceCheck/App Attest integration
    return 'NOT_SUPPORTED';
  }
}
