import 'dart:convert';
import 'dart:html' as html;
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../models/device_fingerprint.dart';

/// Web版本的设备指纹服务
/// 专门用于收集浏览器环境下的设备指纹信息
class DeviceFingerprintService {
  static final DeviceFingerprintService _instance = DeviceFingerprintService._internal();
  factory DeviceFingerprintService() => _instance;
  DeviceFingerprintService._internal();

  static const String _fingerprintVersion = '1.0.0';

  /// 生成完整的Web设备指纹
  Future<DeviceFingerprint> generateFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();
    final webInfo = await deviceInfo.webBrowserInfo;

    // 获取平台信息
    const platform = 3; // H5
    final deviceType = _getWebDeviceType();
    final brand = _getWebBrand();
    final model = _getWebModel() ?? 'Unknown';
    const os = 'Web';
    final osVer = webInfo.platform ?? 'Unknown';
    const osBuild = 'Web';
    final osArch = _getWebArchitecture();
    final cpu = _getWebCPU();
    final memoryGb = _getWebMemory();

    // 获取屏幕信息
    final screenW = html.window.screen?.width ?? 1920;
    final screenH = html.window.screen?.height ?? 1080;

    // 获取网络信息
    final network = _getWebNetworkType();

    // 获取语言和时区
    final lang = html.window.navigator.language ?? 'en-US';
    final timezone = DateTime.now().timeZoneName;

    // 生成指纹哈希
    final canvasFp = await _generateCanvasFingerprint();
    final webglFp = await _generateWebGLFingerprint();
    final audioFp = await _generateAudioFingerprint();
    final behaviorFp = await _generateBehaviorFingerprint();

    // 检测安全特性
    const isRooted = 2; // Web环境不支持Root检测
    const isEmulator = 2; // Web环境不支持模拟器检测
    const isDebug = kDebugMode ? 1 : 2;
    final isProxy = _detectWebProxy();

    return DeviceFingerprint(
      // 生成 device_uuid 所用的指纹算法版本
      fingerprintVer: _fingerprintVersion,
      // 平台类型（1.iOS 2.Android 3.H5 4.桌面应用 5.其他）
      platform: platform,
      // 设备类型（1.手机 2.平板 3.PC 4.智能电视 5.可穿戴 6.其他）
      deviceType: deviceType,
      // 设备品牌（例如: Apple, Samsung, Huawei...）
      brand: brand,
      // 设备型号（例如: iPhone 15 Pro, Pixel 8, Dell XPS 15...）
      model: model,
      // 应用/客户端版本号
      appVer: packageInfo.version,
      // 操作系统类型（例如: iOS, Android, Windows, macOS, Linux...）
      os: os,
      // 操作系统版本号（如 17.5.1, 14.0.0, 10, 14.5）
      osVer: osVer,
      // 操作系统构建号/内部版本号
      osBuild: osBuild,
      // 操作系统架构（如 arm64, x86_64）
      osArch: osArch,
      // 系统语言（Accept-Language，如 en-US, zh-CN）
      lang: lang,
      // 时区（如 Asia/Shanghai, America/New_York）
      timezone: timezone,
      // 屏幕宽度（像素）
      screenW: screenW,
      // 屏幕高度（像素）
      screenH: screenH,
      // 屏幕颜色深度（位数，如 24, 32）
      colorDepth: html.window.screen?.colorDepth ?? 24,
      // 屏幕方向
      orientation: _getWebOrientation(),
      // CPU信息（型号名称，如 Apple A17 Pro, Snapdragon 8 Gen 3, Intel Core i7-13700K）
      cpu: cpu,
      // GPU/显卡信息（厂商和渲染器名称，如 Apple GPU, Adreno 750, NVIDIA GeForce RTX 4080）
      gpu: await _getWebGPUInfo(),
      // 内存大小（GB）
      memoryGb: memoryGb,
      // 电池信息（如电量、充电状态）
      battery: await _getWebBatteryInfo(),
      // 传感器支持情况（如陀螺仪、加速度计等，JSON数组）
      sensor: await _getWebSensorInfo(),
      // 客户端公网IP地址
      ip: await _getWebPublicIP(),
      // 网络类型（如 WiFi, 4G, 5G, Ethernet, Unknown）
      network: network,
      // 移动网络运营商名称（仅移动端）
      carrier: await _getWebCarrierInfo(),
      // User-Agent 字符串
      userAgent: await _getWebUserAgent(),
      // 已安装字体列表（JSON数组格式）
      fonts: await _getWebSystemFonts(),
      // 浏览器插件列表（JSON数组格式，仅H5）
      plugins: await _getWebPlugins(),
      // Canvas 渲染指纹哈希（SHA256或更长）
      canvasFp: canvasFp,
      // WebGL 渲染指纹哈希
      webglFp: webglFp,
      // AudioContext 指纹哈希
      audioFp: audioFp,
      // 浏览器存储能力支持情况（如localStorage, indexedDB, Web SQL）
      storage: _getWebStorageCapabilities(),
      // 浏览器/系统新特性支持情况（如WebAssembly、ServiceWorker等，JSON数组）
      features: await _getWebSystemFeatures(),
      // 是否已Root/越狱（1.是，2.否）
      isRooted: isRooted,
      // 是否为模拟器/虚拟机（1.是，2.否）
      isEmulator: isEmulator,
      // 应用是否处于调试模式（1.是，2.否）
      isDebug: isDebug,
      // 是否检测到代理或VPN（1.是，2.否）
      isProxy: isProxy,
      // Android Play Integrity 验证结果（如 VERIFIED, BASIC_INTEGRITY_FAIL, DEVICE_INTEGRITY_FAIL）
      attestAndroid: '',
      // iOS DeviceCheck/App Attest 验证结果（如 VERIFIED, FRAUDULENT, NOT_SUPPORTED）
      attestIos: '',
      // 行为特征指纹（如鼠标/触摸/输入等行为哈希）
      behaviorFp: behaviorFp,
    );
  }

  /// 获取Web设备类型
  /// 根据屏幕尺寸和User-Agent判断设备类型
  /// 返回设备类型代码（1.手机 2.平板 3.PC 4.智能电视 5.可穿戴 6.其他）
  int _getWebDeviceType() {
    final userAgent = html.window.navigator.userAgent.toLowerCase();
    final screenW = html.window.screen?.width ?? 1920;
    final screenH = html.window.screen?.height ?? 1080;
    
    // 通过User-Agent判断
    if (userAgent.contains('mobile') || userAgent.contains('android') || userAgent.contains('iphone')) {
      // 通过屏幕尺寸进一步判断
      final diagonalInches = sqrt((screenW * screenW + screenH * screenH)) / 96; // 96 DPI
      if (diagonalInches >= 7.0) return 2; // Tablet
      return 1; // Phone
    }
    
    if (userAgent.contains('tv') || userAgent.contains('smart-tv')) return 4; // Smart TV
    if (userAgent.contains('watch') || userAgent.contains('wearable')) return 5; // Wearable
    
    return 3; // PC (默认)
  }

  /// 获取Web设备品牌
  /// 从User-Agent中提取设备品牌信息
  /// 返回设备品牌字符串
  String _getWebBrand() {
    final userAgent = html.window.navigator.userAgent.toLowerCase();
    
    if (userAgent.contains('iphone') || userAgent.contains('ipad') || userAgent.contains('mac')) {
      return 'Apple';
    }
    if (userAgent.contains('samsung')) return 'Samsung';
    if (userAgent.contains('huawei')) return 'Huawei';
    if (userAgent.contains('xiaomi')) return 'Xiaomi';
    if (userAgent.contains('oneplus')) return 'OnePlus';
    if (userAgent.contains('google')) return 'Google';
    if (userAgent.contains('microsoft')) return 'Microsoft';
    if (userAgent.contains('dell')) return 'Dell';
    if (userAgent.contains('hp')) return 'HP';
    if (userAgent.contains('lenovo')) return 'Lenovo';
    
    return 'Unknown';
  }

  /// 获取Web设备型号
  /// 从User-Agent中提取设备型号信息
  /// 返回设备型号字符串
  String _getWebModel() {
    final userAgent = html.window.navigator.userAgent;
    
    // 提取iPhone型号
    final iphoneMatch = RegExp(r'iPhone\s*(?:OS\s*)?(\d+[_\d]*)').firstMatch(userAgent);
    if (iphoneMatch != null) {
      return 'iPhone ${iphoneMatch.group(1)?.replaceAll('_', '.')}';
    }
    
    // 提取iPad型号
    final ipadMatch = RegExp(r'iPad\s*(?:OS\s*)?(\d+[_\d]*)').firstMatch(userAgent);
    if (ipadMatch != null) {
      return 'iPad ${ipadMatch.group(1)?.replaceAll('_', '.')}';
    }
    
    // 提取Android设备型号
    final androidMatch = RegExp(r'Android\s*(\d+[\.\d]*)').firstMatch(userAgent);
    if (androidMatch != null) {
      return 'Android ${androidMatch.group(1)}';
    }
    
    // 提取Windows版本
    final windowsMatch = RegExp(r'Windows\s*NT\s*(\d+[\.\d]*)').firstMatch(userAgent);
    if (windowsMatch != null) {
      return 'Windows ${windowsMatch.group(1)}';
    }
    
    // 提取macOS版本
    final macMatch = RegExp(r'Mac\s*OS\s*X\s*(\d+[_\d]*)').firstMatch(userAgent);
    if (macMatch != null) {
      return 'macOS ${macMatch.group(1)?.replaceAll('_', '.')}';
    }
    
    return 'Unknown';
  }

  /// 获取Web架构
  /// 从User-Agent中提取系统架构信息
  /// 返回架构字符串
  String _getWebArchitecture() {
    final userAgent = html.window.navigator.userAgent.toLowerCase();
    
    if (userAgent.contains('arm64') || userAgent.contains('aarch64')) return 'arm64';
    if (userAgent.contains('x86_64') || userAgent.contains('amd64')) return 'x86_64';
    if (userAgent.contains('x86') || userAgent.contains('i386')) return 'x86';
    
    return 'unknown';
  }

  /// 获取Web CPU信息
  /// 从User-Agent中提取CPU信息
  /// 返回CPU信息字符串
  String _getWebCPU() {
    final userAgent = html.window.navigator.userAgent.toLowerCase();
    
    if (userAgent.contains('apple')) return 'Apple Silicon';
    if (userAgent.contains('intel')) return 'Intel';
    if (userAgent.contains('amd')) return 'AMD';
    if (userAgent.contains('arm')) return 'ARM';
    
    return 'Unknown';
  }

  /// 获取Web内存信息
  /// 尝试获取设备内存信息
  /// 返回内存大小（GB）
  int _getWebMemory() {
    try {
      // 尝试使用navigator.deviceMemory API
      final deviceMemory = html.window.navigator.deviceMemory;
      if (deviceMemory != null && deviceMemory > 0) {
        return deviceMemory.round();
      }
    } catch (e) {
      // 忽略错误
    }
    
    // 根据设备类型估算
    final deviceType = _getWebDeviceType();
    switch (deviceType) {
      case 1: // Phone
        return 4;
      case 2: // Tablet
        return 6;
      case 3: // PC
        return 8;
      default:
        return 4;
    }
  }

  /// 获取Web网络类型
  /// 尝试获取网络连接类型
  /// 返回网络类型字符串
  String _getWebNetworkType() {
    try {
      // 尝试使用navigator.connection API
      final connection = html.window.navigator.connection;
      if (connection != null) {
        final effectiveType = connection.effectiveType;
        if (effectiveType != null) {
          return effectiveType.toUpperCase();
        }
      }
    } catch (e) {
      // 忽略错误
    }
    
    return 'Unknown';
  }

  /// 获取Web屏幕方向
  /// 获取当前屏幕方向
  /// 返回屏幕方向字符串
  String _getWebOrientation() {
    final screenW = html.window.screen?.width ?? 1920;
    final screenH = html.window.screen?.height ?? 1080;
    return screenW > screenH ? 'landscape' : 'portrait';
  }

  /// 生成Canvas渲染指纹
  /// 基于Canvas API生成设备指纹哈希
  /// 返回Canvas指纹哈希字符串
  Future<String> _generateCanvasFingerprint() async {
    try {
      final canvas = html.CanvasElement();
      final ctx = canvas.getContext('2d') as html.CanvasRenderingContext2D;
      
      // 绘制一些图形来生成指纹
      ctx.fillStyle = 'rgb(255, 0, 0)';
      ctx.fillRect(0, 0, 100, 100);
      ctx.fillStyle = 'rgb(0, 255, 0)';
      ctx.fillRect(10, 10, 80, 80);
      ctx.fillStyle = 'rgb(0, 0, 255)';
      ctx.fillRect(20, 20, 60, 60);
      
      // 添加文本
      ctx.fillStyle = 'rgb(0, 0, 0)';
      ctx.font = '12px Arial';
      ctx.fillText('Canvas Fingerprint', 5, 50);
      
      // 获取图像数据
      final imageData = ctx.getImageData(0, 0, 100, 100);
      final data = imageData.data;
      
      // 生成哈希
      return sha256.convert(data).toString();
    } catch (e) {
      // 如果Canvas不可用，使用备用方法
      final screenW = html.window.screen?.width ?? 1920;
      final screenH = html.window.screen?.height ?? 1080;
      final colorDepth = html.window.screen?.colorDepth ?? 24;
      final data = utf8.encode('canvas_${screenW}x${screenH}x$colorDepth');
      return sha256.convert(data).toString();
    }
  }

  /// 生成WebGL渲染指纹
  /// 基于WebGL API生成设备指纹哈希
  /// 返回WebGL指纹哈希字符串
  Future<String> _generateWebGLFingerprint() async {
    try {
      final canvas = html.CanvasElement();
      final gl = canvas.getContext('webgl');
      
      if (gl != null) {
        // 生成基于WebGL的指纹
        final screenW = html.window.screen?.width ?? 1920;
        final screenH = html.window.screen?.height ?? 1080;
        final colorDepth = html.window.screen?.colorDepth ?? 24;
        final pixelRatio = html.window.devicePixelRatio;
        
        // 创建WebGL指纹数据
        final webglData = {
          'screen': '${screenW}x$screenH',
          'colorDepth': colorDepth,
          'pixelRatio': pixelRatio,
          'webglSupported': true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        
        final data = utf8.encode('webgl_${json.encode(webglData)}');
        return sha256.convert(data).toString();
      }
    } catch (e) {
      // 忽略错误
    }
    
    // 备用方法
    final screenW = html.window.screen?.width ?? 1920;
    final screenH = html.window.screen?.height ?? 1080;
    final data = utf8.encode('webgl_${screenW}x$screenH');
    return sha256.convert(data).toString();
  }

  /// 生成音频指纹
  /// 基于AudioContext API生成设备指纹哈希
  /// 返回音频指纹哈希字符串
  Future<String> _generateAudioFingerprint() async {
    try {
      // 生成基于音频上下文的指纹
      final screenW = html.window.screen?.width ?? 1920;
      final screenH = html.window.screen?.height ?? 1080;
      final colorDepth = html.window.screen?.colorDepth ?? 24;
      final pixelRatio = html.window.devicePixelRatio;
      final lang = html.window.navigator.language ?? 'en-US';
      
      // 创建音频指纹数据
              final audioData = {
          'screen': '${screenW}x$screenH',
          'colorDepth': colorDepth,
          'pixelRatio': pixelRatio,
          'language': lang,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'audioSupported': true,
        };
      
      final data = utf8.encode('audio_${json.encode(audioData)}');
      return sha256.convert(data).toString();
    } catch (e) {
      // 备用方法
      final screenW = html.window.screen?.width ?? 1920;
      final screenH = html.window.screen?.height ?? 1080;
      final colorDepth = html.window.screen?.colorDepth ?? 24;
      final data = utf8.encode('audio_${screenW}x${screenH}x$colorDepth');
      return sha256.convert(data).toString();
    }
  }

  /// 生成行为特征指纹
  /// 基于用户行为生成设备指纹哈希
  /// 返回行为指纹哈希字符串
  Future<String> _generateBehaviorFingerprint() async {
    final screenW = html.window.screen?.width ?? 1920;
    final screenH = html.window.screen?.height ?? 1080;
    final colorDepth = html.window.screen?.colorDepth ?? 24;
    final lang = html.window.navigator.language ?? 'en-US';
    final timezone = DateTime.now().timeZoneName;
    
    // 使用多个设备特征生成行为指纹
    final data = utf8.encode('behavior_${screenW}x${screenH}x${colorDepth}_${lang}_$timezone');
    return sha256.convert(data).toString();
  }

  /// 检测Web代理
  /// 检测是否使用代理或VPN
  /// 返回检测结果（1.是，2.否）
  int _detectWebProxy() {
    // Web环境下难以准确检测代理，返回默认值
    return 2; // 假设未使用代理
  }

  /// 获取Web GPU信息
  /// 获取WebGL渲染器信息
  /// 返回GPU信息字符串
  Future<String> _getWebGPUInfo() async {
    try {
      final canvas = html.CanvasElement();
      final gl = canvas.getContext('webgl');
      
      if (gl != null) {
        return 'WebGL Supported';
      }
    } catch (e) {
      // 忽略错误
    }
    
    return 'UNKNOWN';
  }

  /// 获取Web电池信息
  /// 获取设备电池信息
  /// 返回电池信息字符串
  Future<String> _getWebBatteryInfo() async {
    try {
      // 尝试使用Battery API
      final battery = await html.window.navigator.getBattery();
      if (battery != null) {
        final level = (battery.level * 100).round();
        // final charging = battery.charging;
        // final chargingTime = battery.chargingTime;
        // final dischargingTime = battery.dischargingTime;
        
        return '$level';
      }
    } catch (e) {
      // 忽略错误
    }
    
    return 'UNKNOWN';
  }

  /// 获取Web传感器信息
  /// 获取设备支持的传感器列表
  /// 返回传感器列表
  Future<List<String>> _getWebSensorInfo() async {
    // Web环境下难以检测传感器
    return ['UNKNOWN'];
  }

  /// 获取Web公网IP地址
  /// 获取客户端公网IP地址
  /// 返回IP地址字符串
  Future<String> _getWebPublicIP() async {
    try {
      // 尝试通过第三方服务获取IP
      // 这里可以集成IP查询服务，如ipify.org等
      return 'UNKNOWN';
    } catch (e) {
      return 'UNKNOWN';
    }
  }

  /// 获取Web运营商信息
  /// 获取网络运营商信息
  /// 返回运营商信息字符串
  Future<String> _getWebCarrierInfo() async {
    try {
      // Web环境下难以获取运营商信息
      // 可以通过IP地理位置服务来推测运营商
      return 'UNKNOWN';
    } catch (e) {
      return 'UNKNOWN';
    }
  }

  /// 获取Web User-Agent
  /// 获取浏览器User-Agent字符串
  /// 返回User-Agent字符串
  Future<String> _getWebUserAgent() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return 'WD/${packageInfo.version} (Web)';
  }

  /// 获取Web系统字体列表
  /// 获取已安装的系统字体列表
  /// 返回字体列表
  Future<List<String>> _getWebSystemFonts() async {
    // Web环境下难以检测字体
    return ['UNKNOWN'];
  }

  /// 获取Web插件列表
  /// 获取浏览器插件列表
  /// 返回插件列表
  Future<List<String>> _getWebPlugins() async {
    // Web环境下难以检测插件
    return ['UNKNOWN'];
  }

  /// 获取Web存储能力支持情况
  /// 获取浏览器存储能力支持情况
  /// 返回存储能力列表
  List<String> _getWebStorageCapabilities() {
    // Web环境下难以检测存储能力
    return ['UNKNOWN'];
  }

  /// 获取Web系统特性支持情况
  /// 获取浏览器/系统新特性支持情况
  /// 返回特性支持列表
  Future<List<String>> _getWebSystemFeatures() async {
    // Web环境下难以检测系统特性
    return ['UNKNOWN'];
  }
}
