import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/device_fingerprint.dart';

/// Service for collecting device fingerprint information
class DeviceFingerprintService {
  static final DeviceFingerprintService _instance = DeviceFingerprintService._internal();
  factory DeviceFingerprintService() => _instance;
  DeviceFingerprintService._internal();

  static const String _fingerprintVersion = '1.0.0';

  /// Generate complete device fingerprint
  Future<DeviceFingerprint> generateFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();
    final connectivity = Connectivity();

    // Get platform-specific device information
    int platform = 5; // Default: Other
    int deviceType = 1; // Default: Phone
    String brand = 'Unknown';
    String model = 'Unknown';
    String os = 'Unknown';
    String osVer = 'Unknown';
    String osBuild = 'Unknown';
    String osArch = 'Unknown';
    String cpu = 'Unknown';
    int memoryGb = 0;

    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      platform = 1; // iOS
      deviceType = _getIOSDeviceType(iosInfo.model);
      brand = 'Apple';
      model = iosInfo.model;
      os = 'iOS';
      osVer = iosInfo.systemVersion;
      osBuild = iosInfo.systemVersion;
      osArch = _getIOSArchitecture(iosInfo.model);
      cpu = _getIOSCPU(iosInfo.model);
      memoryGb = _getIOSMemory(iosInfo.model);
    } else if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      platform = 2; // Android
      deviceType = _getAndroidDeviceType(androidInfo.model);
      brand = androidInfo.brand;
      model = androidInfo.model;
      os = 'Android';
      osVer = androidInfo.version.release;
      osBuild = androidInfo.version.incremental;
      osArch = androidInfo.supported64BitAbis.isNotEmpty
          ? androidInfo.supported64BitAbis.first
          : androidInfo.supported32BitAbis.isNotEmpty
              ? androidInfo.supported32BitAbis.first
              : 'unknown';
      cpu = androidInfo.hardware;
      memoryGb = _estimateAndroidMemory();
    }

    // Get screen information using MediaQuery fallback
    final screenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final screenW = screenSize.width.toInt();
    final screenH = screenSize.height.toInt();

    // Get network information
    final connectivityResults = await connectivity.checkConnectivity();
    final network =
        _getNetworkType(connectivityResults.isNotEmpty ? connectivityResults.first : ConnectivityResult.none);

    // Get locale and timezone
    final locale = ui.PlatformDispatcher.instance.locale;
    final lang = '${locale.languageCode}-${locale.countryCode}';
    final timezone = DateTime.now().timeZoneName;

    // Generate fingerprint hashes
    final canvasFp = await _generateCanvasFingerprint();
    final webglFp = await _generateWebGLFingerprint();
    final audioFp = await _generateAudioFingerprint();
    final behaviorFp = await _generateBehaviorFingerprint();

    // Detect security features
    final isRooted = await _detectRootJailbreak();
    final isEmulator = await _detectEmulator();
    const isDebug = kDebugMode ? 1 : 2;
    final isProxy = await _detectProxy();

    return DeviceFingerprint(
      fingerprintVer: _fingerprintVersion,
      platform: platform,
      deviceType: deviceType,
      brand: brand,
      model: model,
      appVer: packageInfo.version,
      os: os,
      osVer: osVer,
      osBuild: osBuild,
      osArch: osArch,
      lang: lang,
      timezone: timezone,
      screenW: screenW,
      screenH: screenH,
      colorDepth: 32, // Standard for mobile devices
      orientation: screenW > screenH ? 'landscape' : 'portrait',
      cpu: cpu,
      gpu: await _getGPUInfo(),
      memoryGb: memoryGb,
      battery: await _getBatteryInfo(),
      sensor: await _getSensorInfo(),
      ip: await _getPublicIP(),
      network: network,
      carrier: await _getCarrierInfo(),
      userAgent: await _getUserAgent(),
      fonts: await _getSystemFonts(),
      plugins: [], // Not applicable for mobile apps
      canvasFp: canvasFp,
      webglFp: webglFp,
      audioFp: audioFp,
      storage: _getStorageCapabilities(),
      features: await _getSystemFeatures(),
      isRooted: isRooted,
      isEmulator: isEmulator,
      isDebug: isDebug,
      isProxy: isProxy,
      attestAndroid: Platform.isAndroid ? await _getAndroidAttestation() : '',
      attestIos: Platform.isIOS ? await _getIOSAttestation() : '',
      behaviorFp: behaviorFp,
    );
  }

  // Helper methods for device type detection
  int _getIOSDeviceType(String model) {
    if (model.toLowerCase().contains('ipad')) return 2; // Tablet
    return 1; // Phone
  }

  int _getAndroidDeviceType(String model) {
    final modelLower = model.toLowerCase();
    if (modelLower.contains('tablet') || modelLower.contains('tab')) return 2; // Tablet
    return 1; // Phone
  }

  String _getIOSArchitecture(String model) {
    // Most modern iOS devices use arm64
    return 'arm64';
  }

  String _getIOSCPU(String model) {
    // Simplified CPU detection for iOS
    if (model.contains('iPhone15') || model.contains('iPhone 15')) return 'Apple A17 Pro';
    if (model.contains('iPhone14') || model.contains('iPhone 14')) return 'Apple A16 Bionic';
    if (model.contains('iPhone13') || model.contains('iPhone 13')) return 'Apple A15 Bionic';
    return 'Apple A-Series';
  }

  int _getIOSMemory(String model) {
    // Simplified memory estimation for iOS
    if (model.contains('Pro')) return 8;
    if (model.contains('iPhone')) return 6;
    if (model.contains('iPad')) return 4;
    return 4;
  }

  int _estimateAndroidMemory() {
    // This is an estimation - actual memory detection requires native code
    return 4; // Default to 4GB
  }

  String _getNetworkType(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return '4G'; // Simplified - could be 3G, 4G, 5G
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      default:
        return 'Unknown';
    }
  }

  // Fingerprint generation methods
  Future<String> _generateCanvasFingerprint() async {
    // Simplified canvas fingerprint - in a real implementation,
    // you would render specific patterns and hash the result
    final random = Random();
    final data = List.generate(32, (index) => random.nextInt(256));
    return sha256.convert(data).toString();
  }

  Future<String> _generateWebGLFingerprint() async {
    // Simplified WebGL fingerprint
    final random = Random();
    final data = List.generate(32, (index) => random.nextInt(256));
    return sha256.convert(data).toString();
  }

  Future<String> _generateAudioFingerprint() async {
    // Simplified audio fingerprint
    final random = Random();
    final data = List.generate(32, (index) => random.nextInt(256));
    return sha256.convert(data).toString();
  }

  Future<String> _generateBehaviorFingerprint() async {
    // Simplified behavior fingerprint
    final random = Random();
    final data = List.generate(32, (index) => random.nextInt(256));
    return sha256.convert(data).toString();
  }

  // Security detection methods
  Future<int> _detectRootJailbreak() async {
    try {
      if (Platform.isAndroid) {
        // Check for common root indicators
        final rootPaths = ['/system/app/Superuser.apk', '/sbin/su', '/system/bin/su', '/system/xbin/su'];
        for (final path in rootPaths) {
          if (await File(path).exists()) return 1; // Rooted
        }
      } else if (Platform.isIOS) {
        // Check for common jailbreak indicators
        final jailbreakPaths = ['/Applications/Cydia.app', '/var/lib/cydia', '/etc/apt'];
        for (final path in jailbreakPaths) {
          if (await Directory(path).exists()) return 1; // Jailbroken
        }
      }
      return 2; // Not rooted/jailbroken
    } catch (e) {
      return 2; // Assume not rooted if detection fails
    }
  }

  Future<int> _detectEmulator() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;

        // Check for common emulator indicators
        final emulatorIndicators = ['google_sdk', 'Emulator', 'Android SDK built for x86'];
        if (emulatorIndicators
            .any((indicator) => androidInfo.model.contains(indicator) || androidInfo.product.contains(indicator))) {
          return 1; // Emulator
        }
      }
      return 2; // Not emulator
    } catch (e) {
      return 2; // Assume not emulator if detection fails
    }
  }

  Future<int> _detectProxy() async {
    // Simplified proxy detection
    return 2; // Not using proxy (default)
  }

  // Information gathering methods
  Future<String> _getGPUInfo() async {
    // GPU information is not easily accessible in Flutter
    if (Platform.isIOS) return 'Apple GPU';
    if (Platform.isAndroid) return 'Adreno/Mali/PowerVR';
    return 'Unknown';
  }

  Future<String> _getBatteryInfo() async {
    // Battery information requires platform-specific implementation
    return 'Unknown';
  }

  Future<List<String>> _getSensorInfo() async {
    // Sensor information requires platform-specific implementation
    return ['accelerometer', 'gyroscope', 'magnetometer'];
  }

  Future<String> _getPublicIP() async {
    // Public IP detection requires network call
    return '0.0.0.0';
  }

  Future<String> _getCarrierInfo() async {
    // Carrier information requires platform-specific implementation
    return 'Unknown';
  }

  Future<String> _getUserAgent() async {
    // Generate a user agent string
    final packageInfo = await PackageInfo.fromPlatform();
    return 'WD/${packageInfo.version} (${Platform.operatingSystem})';
  }

  Future<List<String>> _getSystemFonts() async {
    // System fonts are not easily accessible in Flutter
    return ['System Font'];
  }

  List<String> _getStorageCapabilities() {
    return ['SharedPreferences', 'SQLite', 'File System'];
  }

  Future<List<String>> _getSystemFeatures() async {
    return ['Camera', 'GPS', 'Bluetooth', 'NFC'];
  }

  Future<String> _getAndroidAttestation() async {
    // Android attestation requires Google Play Services integration
    return 'NOT_SUPPORTED';
  }

  Future<String> _getIOSAttestation() async {
    // iOS attestation requires DeviceCheck/App Attest integration
    return 'NOT_SUPPORTED';
  }
}
