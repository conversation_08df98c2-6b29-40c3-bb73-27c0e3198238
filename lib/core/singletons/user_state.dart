import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:wd/core/models/entities/invite_code_entity.dart';
import 'package:wd/core/models/entities/login_entity.dart';
import 'package:wd/core/models/entities/tc_sdk_config_entity.dart';
import 'package:wd/core/models/entities/user_info_entity.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';
import 'package:wd/core/models/entities/video_entity.dart';

class UserState extends Equatable {
  /// 是否已登录
  final bool isLogin;

  /// 登录信息
  final LoginTokenUser? loginInfo;

  /// 用户信息
  final UserInfoEntity? userInfo;

  /// 余额信息
  final UserBalanceEntity? balanceInfo;

  /// VIP信息
  final UserVipEntity? vipInfo;

  /// 邀请码信息
  final InviteCodeEntity? inviteInfo;

  /// 视频vip信息
  final VideoVipRemainDayEntity? videoVipInfo;

  /// 腾讯im相关配置信息
  final TCSDKConfigEntity? tencentConfig;

  /// 首页提现订单状态浮动按钮标题
  final String withdrawFloatBtnTitle;

  /// 推送token
  String? remotePushDeviceToken;

  UserState({
    this.isLogin = false,
    this.loginInfo,
    this.userInfo,
    this.balanceInfo,
    this.vipInfo,
    this.inviteInfo,
    this.videoVipInfo,
    this.tencentConfig,
    this.remotePushDeviceToken,
    this.withdrawFloatBtnTitle = "",
  });

  UserState copyWith({
    bool? isLogin,
    LoginTokenUser? loginInfo,
    ValueGetter<UserInfoEntity?>? userInfo,
    UserBalanceEntity? balanceInfo,
    UserVipEntity? vipInfo,
    InviteCodeEntity? inviteInfo,
    VideoVipRemainDayEntity? videoVipInfo,
    TCSDKConfigEntity? tencentConfig,
    String? remotePushDeviceToken,
    String? withdrawFloatBtnTitle,
  }) {
    return UserState(
      isLogin: isLogin ?? this.isLogin,
      loginInfo: loginInfo ?? this.loginInfo,
      userInfo: userInfo != null ? userInfo() : this.userInfo,
      balanceInfo: balanceInfo ?? this.balanceInfo,
      vipInfo: vipInfo ?? this.vipInfo,
      inviteInfo: inviteInfo ?? this.inviteInfo,
      videoVipInfo: videoVipInfo ?? this.videoVipInfo,
      tencentConfig: tencentConfig ?? this.tencentConfig,
      remotePushDeviceToken: remotePushDeviceToken ?? this.remotePushDeviceToken,
      withdrawFloatBtnTitle: withdrawFloatBtnTitle ?? this.withdrawFloatBtnTitle,
    );
  }

  @override
  List<Object?> get props => [
        isLogin,
        loginInfo,
        userInfo,
        balanceInfo,
        vipInfo,
        videoVipInfo,
        inviteInfo,
        tencentConfig,
        remotePushDeviceToken,
        withdrawFloatBtnTitle,
      ];
}
