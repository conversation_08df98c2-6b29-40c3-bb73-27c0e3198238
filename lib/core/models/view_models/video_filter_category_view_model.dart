// import 'package:wd/core/models/entities/video_filter_entity.dart';

// class VideoCategoryItem {
//   final String title;
//   final String value;
//   bool isSel;

//   VideoCategoryItem({
//     required this.title,
//     required this.value,
//     this.isSel = false,
//   });
// }

// class VideoFilterCategoryViewModel {
//   final String title;
//   final List<VideoCategoryItem> items;
//   final String type;

//   VideoFilterCategoryViewModel({
//     required this.title,
//     required this.items,
//     required this.type,
//   });

//   factory VideoFilterCategoryViewModel.fromFilterCategory(VideoFilterEntity category) {
//     List<VideoCategoryItem> items = category.values.map((value) {
//       if (value == '全部') {
//         return VideoCategoryItem(title: value, value: '', isSel: true);
//       } else {
//         return VideoCategoryItem(title: value, value: value, isSel: false);
//       }
//     }).toList();

//     return VideoFilterCategoryViewModel(
//       title: category.videoCategory,
//       items: items,
//       type: category.videoCategoryId,
//     );
//   }
// }
