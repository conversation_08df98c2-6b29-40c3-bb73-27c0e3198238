import 'package:wd/core/models/entities/activity_category_entity.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/activity_task_record_entity.dart';
import 'package:wd/core/utils/http/https.dart';

class ActivityApi {
  /// Activity 活动分类列表, type活动类型 1-游戏活动 2-自助领取
  static Future<List<T>> fetchActivityCategoryList<T>(
    int type, {
    required T Function(ActivityCategory) mapper,
  }) async {
    ResponseModel response = await Http().request<ActivityCategoryListEntity>(
      ApiConstants.activityCategoryList,
      params: {
        "activeType": type // 活动类型 1-游戏活动 2-自助领取
      },
      method: HttpMethod.get,
      needSignIn: false,
    );
    if (response.isSuccess && response.data != null) {
      final list = response.data.list;
      if (list is List<ActivityCategory>) {
        return list.map((e) => mapper(e)).toList();
      }
    }
    return [];
  }

  /// Activity 活动列表
  static Future<ActivityListEntity> fetchActivityList({required int pageNo, required int activeCategory}) async {
    ResponseModel response = await Http().request<ActivityListEntity>(
      ApiConstants.activityList,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
        "activeCategory": activeCategory,
      },
      needSignIn: false,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return ActivityListEntity();
    }
  }

  /// Task 自助领取活动列表
  static Future<List<ActivityTask>> fetchTaskList({required int activeCategory}) async {
    ResponseModel response = await Http().request<ActivityTaskListEntity>(
      ApiConstants.taskList,
      params: {"activeCategory": activeCategory},
      method: HttpMethod.get,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data.list;
      return model;
    }
    return [];
  }

  /// 获取彩金领取状态 0:未领取,1:已领取,2:已过期, -1未知
  static Future<int> fetchActivityCollectionStatus({required int id}) async {
    ResponseModel response = await Http().request(ApiConstants.activityCollectionStatus, 
      params: {
        'id': id
      },
    );
    if (response.isSuccess && response.data != null) {
      return response.data;
    }
    return -1;
  }

  static Future<bool> completeActivityTask({required int id}) async {
    ResponseModel response = await Http().request(
      ApiConstants.activityTaskComplete, // New endpoint
      params: {"id": id},
      method: HttpMethod.get,
    );
    return response.isSuccess;
  }

  static Future<({bool isSuccess, String msg})> applyActivity({required int id, required int operationType}) async {
    ResponseModel response = await Http().request(ApiConstants.activityApply,
        params: {
          "activeId": id,
          "operationType": operationType,
        },
        needShowToast: false);
    return (isSuccess: response.isSuccess, msg: response.msg ?? "");
  }

  // 自助活动领取记录
  static Future<ActivityTaskRecordEntity?> fetchActivityTaskRecordList({required int pageNo}) async {
    ResponseModel response = await Http().request<ActivityTaskRecordEntity>(
      ApiConstants.activityTaskRecordList,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
      },
    );

    if (response.isSuccess && response.data != null) {
      return response.data;
    }
    return null;
  }
}
