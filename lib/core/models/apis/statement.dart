import 'package:wd/core/models/entities/statement_entity.dart';
import 'package:wd/core/models/entities/statement_filter_way_entity.dart';

import '../../utils/http/https.dart';

class StatementApi {
  /// statement 账变记录
  static Future<StatementEntity?> fetchStatement({
    required int pageNo,
    required int startDate,
    required int endDate,
    required String wayCode,
    required String typeCode,
  }) async {
    ResponseModel response = await Http().request<StatementEntity>(
      ApiConstants.userStatement,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
        "startDate": startDate,
        "endDate": endDate,
        "wayCode": wayCode,
        "typeCode": typeCode,
      },
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// 账变记录筛选-方式列表
  static Future<List<StatementFilterWay>> fetchStatementFilterWay() async {
    ResponseModel response = await Http().request<StatementFilterWayList>(
        ApiConstants.userStatementFilterWay);
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }

  /// 账变记录筛选-类型列表
  static Future<List<StatementFilterType>> fetchStatementFilterType() async {
    ResponseModel response = await Http().request<StatementFilterTypeList>(
        ApiConstants.userStatementFilterType);
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }

}
