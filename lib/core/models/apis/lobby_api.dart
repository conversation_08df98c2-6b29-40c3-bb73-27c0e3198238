
import '../../utils/http/https.dart';
import '../entities/bonus_pool_entity.dart';

class LobbyApi {
  /// Check user eligibility for lobby red packet
  static Future<bool> checkLobbyRedPacketEligibility() async {
    ResponseModel response = await Http().request(
      ApiConstants.lobbyRedPacketCheckEligibility,
    );
    return response.isSuccess && response.data == true;
  }

  /// Receive lobby red packet amount
  static Future<double?> receiveLobbyRedPacket() async {
    ResponseModel response = await Http().request(
      ApiConstants.lobbyRedPacketReceive,
    );
    if (response.isSuccess && response.data != null) {
      return (response.data as num).toDouble();
    }
    return null;
  }


  static Future<BonusPoolEntity?> getBonusPool() async {
    ResponseModel response = await Http().request(
      ApiConstants.getLastsBonusPool,
      needSignIn: false,
      needShowToast: false,
    );
    if (response.isSuccess && response.data != null) {
      return BonusPoolEntity.fromJson(response.data);
    }
    return null;
  }
}
