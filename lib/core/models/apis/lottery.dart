import 'package:wd/core/models/entities/bet_record_entity.dart';
import 'package:wd/core/models/entities/lottery_cart_item_entity.dart';
import 'package:wd/core/models/entities/lottery_current_status_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/lottery_odds_entity.dart';
import 'package:wd/core/models/entities/lottery_result_entity.dart';
import 'package:wd/core/models/entities/match_result_entity.dart';
import 'package:wd/core/utils/http/https.dart';

/// 自营彩票
class LotteryApi {
  /// 获取自营彩票列表
  static Future<List<LotteryGroup>> fetchHomeLotteryList() async {
    ResponseModel res = await Http().request<LotteryListEntity>(ApiConstants.lotteryList, needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    }
    return [];
  }

  /// 获取赔率列表
  static Future<List<LotteryOdds>> fetchLotteryOddsList({required int id}) async {
    ResponseModel res = await Http().request<LotteryOddsList>(ApiConstants.lotteryOdds,
        params: {
          'lotteryId': id,
        },
        isFormUrlEncoded: true,
        needSignIn: false,
        needShowToast: false);
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    }
    return [];
  }

  /// 获取最新期数数据
  static Future<LotteryCurrentStatusEntity?> fetchLotteryState({required int id}) async {
    ResponseModel res = await Http().request<LotteryCurrentStatusEntity>(ApiConstants.lotteryCurrentState,
        params: {
          'lotteryId': id,
        },
        isFormUrlEncoded: true,
        needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  /// 获取今日开奖结果
  static Future<LotteryResultEntity?> fetchLotteryTodayResult({required int id}) async {
    ResponseModel res = await Http().request<LotteryResultEntity>(ApiConstants.lotteryTodayResult,
        params: {
          'lotteryId': id,
        },
        isFormUrlEncoded: true,
        needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  /// 提交
  static Future<bool> submitBet({
    required String period,
    required int lotteryId,
    required List<LotteryCartItemEntity> cartList,
  }) async {
    final dataList = cartList.map(
      (model) {
        Map map = {"betAmount": model.betAmount, "id": model.id};
        if (model.option.isNotEmpty) {
          map["option"] = model.option;
        }
        return map;
      },
    ).toList();

    ResponseModel res = await Http().request(ApiConstants.lotterySubmit, params: {
      "betList": dataList,
      "period": period,
      "lotteryId": lotteryId,
    });

    return res.isSuccess;
  }

  static Future<BetRecordEntity?> fetchBetRecordList({
    required int pageNo,
    required int pageSize,
    required int startDate,
    required int endDate,
    required dynamic win,
    required dynamic orderStatus,
    required dynamic lotteryType,
  }) async {
    ResponseModel res = await Http().request<BetRecordEntity>(
      ApiConstants.betRecord,
      params: {
        'pageNo': pageNo,
        'pageSize': pageSize,
        'startDate': startDate,
        'endDate': endDate,
        'lotteryId': lotteryType,
        'win': win,
        'orderStatus': orderStatus,
      },
      needSignIn: true,
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  static Future<MatchResultEntity?> fetchSportsResult({required int id}) async {
    ResponseModel res = await Http().request<MatchResultEntity>(ApiConstants.matchResult, needSignIn: true);
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  static Future<LotteryCurrentStatusEntity?> fetchLotteryStateV2({required int id}) async {
    ResponseModel res = await Http().request<LotteryCurrentStatusEntity>(ApiConstants.lotteryCurrentStateV2,
        params: {
          'lotteryId': id,
        },
        isFormUrlEncoded: true,
        needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data;
    }
    return null;
  }

  /// 彩票点击次数统计
  static Future<bool> submitClickLottery({required int id, required int groupId}) async {
    ResponseModel res = await Http().request(ApiConstants.lotteryClick,
        params: {'lotteryId': id, 'lotteryGroupId': groupId}, needSignIn: false, needShowToast: false);
    return res.isSuccess;
  }

  /// 获取彩票规则
  static Future<String?> fetchLotteryRule({required int id}) async {
    ResponseModel res = await Http().request<String>(
      ApiConstants.lotteryRule,
      params: {'lotteryId': id},
      isFormUrlEncoded: true,
    );
    return res.isSuccess ? res.data : '';
  }
}
