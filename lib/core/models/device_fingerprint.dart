/// Device fingerprint model for device login
class DeviceFingerprint {
  final String fingerprintVer;
  final int platform;
  final int deviceType;
  final String brand;
  final String model;
  final String appVer;
  final String os;
  final String osVer;
  final String osBuild;
  final String osArch;
  final String lang;
  final String timezone;
  final int screenW;
  final int screenH;
  final int colorDepth;
  final String orientation;
  final String cpu;
  final String gpu;
  final int memoryGb;
  final String battery;
  final List<String> sensor;
  final String ip;
  final String network;
  final String carrier;
  final String userAgent;
  final List<String> fonts;
  final List<String> plugins;
  final String canvasFp;
  final String webglFp;
  final String audioFp;
  final List<String> storage;
  final List<String> features;
  final int isRooted;
  final int isEmulator;
  final int isDebug;
  final int isProxy;
  final String attestAndroid;
  final String attestIos;
  final String behaviorFp;

  const DeviceFingerprint({
    required this.fingerprintVer,
    required this.platform,
    required this.deviceType,
    required this.brand,
    required this.model,
    required this.appVer,
    required this.os,
    required this.osVer,
    required this.osBuild,
    required this.osArch,
    required this.lang,
    required this.timezone,
    required this.screenW,
    required this.screenH,
    required this.colorDepth,
    required this.orientation,
    required this.cpu,
    required this.gpu,
    required this.memoryGb,
    required this.battery,
    required this.sensor,
    required this.ip,
    required this.network,
    required this.carrier,
    required this.userAgent,
    required this.fonts,
    required this.plugins,
    required this.canvasFp,
    required this.webglFp,
    required this.audioFp,
    required this.storage,
    required this.features,
    required this.isRooted,
    required this.isEmulator,
    required this.isDebug,
    required this.isProxy,
    required this.attestAndroid,
    required this.attestIos,
    required this.behaviorFp,
  });

  Map<String, dynamic> toJson() {
    return {
      'fingerprintVer': fingerprintVer,
      'platform': platform,
      'deviceType': deviceType,
      'brand': brand,
      'model': model,
      'appVer': appVer,
      'os': os,
      'osVer': osVer,
      'osBuild': osBuild,
      'osArch': osArch,
      'lang': lang,
      'timezone': timezone,
      'screenW': screenW,
      'screenH': screenH,
      'colorDepth': colorDepth,
      'orientation': orientation,
      'cpu': cpu,
      'gpu': gpu,
      'memoryGb': memoryGb,
      'battery': battery,
      'sensor': sensor,
      'ip': ip,
      'network': network,
      'carrier': carrier,
      'userAgent': userAgent,
      'fonts': fonts,
      'plugins': plugins,
      'canvasFp': canvasFp,
      'webglFp': webglFp,
      'audioFp': audioFp,
      'storage': storage,
      'features': features,
      'isRooted': isRooted,
      'isEmulator': isEmulator,
      'isDebug': isDebug,
      'isProxy': isProxy,
      'attestAndroid': attestAndroid,
      'attestIos': attestIos,
      'behaviorFp': behaviorFp,
    };
  }

  factory DeviceFingerprint.fromJson(Map<String, dynamic> json) {
    return DeviceFingerprint(
      fingerprintVer: json['fingerprintVer'] ?? '',
      platform: json['platform'] ?? 0,
      deviceType: json['deviceType'] ?? 0,
      brand: json['brand'] ?? '',
      model: json['model'] ?? '',
      appVer: json['appVer'] ?? '',
      os: json['os'] ?? '',
      osVer: json['osVer'] ?? '',
      osBuild: json['osBuild'] ?? '',
      osArch: json['osArch'] ?? '',
      lang: json['lang'] ?? '',
      timezone: json['timezone'] ?? '',
      screenW: json['screenW'] ?? 0,
      screenH: json['screenH'] ?? 0,
      colorDepth: json['colorDepth'] ?? 0,
      orientation: json['orientation'] ?? '',
      cpu: json['cpu'] ?? '',
      gpu: json['gpu'] ?? '',
      memoryGb: json['memoryGb'] ?? 0,
      battery: json['battery'] ?? '',
      sensor: List<String>.from(json['sensor'] ?? []),
      ip: json['ip'] ?? '',
      network: json['network'] ?? '',
      carrier: json['carrier'] ?? '',
      userAgent: json['userAgent'] ?? '',
      fonts: List<String>.from(json['fonts'] ?? []),
      plugins: List<String>.from(json['plugins'] ?? []),
      canvasFp: json['canvasFp'] ?? '',
      webglFp: json['webglFp'] ?? '',
      audioFp: json['audioFp'] ?? '',
      storage: List<String>.from(json['storage'] ?? []),
      features: List<String>.from(json['features'] ?? []),
      isRooted: json['isRooted'] ?? 2,
      isEmulator: json['isEmulator'] ?? 2,
      isDebug: json['isDebug'] ?? 2,
      isProxy: json['isProxy'] ?? 2,
      attestAndroid: json['attestAndroid'] ?? '',
      attestIos: json['attestIos'] ?? '',
      behaviorFp: json['behaviorFp'] ?? '',
    );
  }

  @override
  String toString() {
    return 'DeviceFingerprint(${toJson()})';
  }
}
