import 'entities/country_entity.dart';

class Country {
  final String name;
  final String code;
  final int areaCode;
  final String flag;

  const Country({
    required this.name,
    required this.code,
    required this.areaCode,
    required this.flag,
  });

  /// Create Country from CountryList entity
  factory Country.fromCountryList(CountryList countryList) {
    return Country(
      name: countryList.areaName ?? '',
      code: countryList.areaIcon ?? '',
      areaCode: int.tryParse(countryList.areaCode ?? '0') ?? 0,
      flag: emoji<PERSON>lag(countryList.areaIcon ?? ''),
    );
  }

  /// Legacy fromJson for backward compatibility
  factory Country.fromJson(Map<String, dynamic> json) {
    // API format: {"areaName":"Canada","areaCode":"1","areaIcon":"CA"}
    return Country(
      name: json['areaName'] as String,
      code: json['areaIcon'] as String,
      areaCode: int.parse(json['areaCode'].toString()),
      flag: emojiFlag(json['areaIcon'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'code': code,
      'areaCode': areaCode,
      'flag': flag,
    };
  }

  @override
  String toString() {
    return 'Country(name: $name, code: $code, areaCode: $areaCode, flag: $flag)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Country &&
        other.name == name &&
        other.code == code &&
        other.areaCode == areaCode &&
        other.flag == flag;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        code.hashCode ^
        areaCode.hashCode ^
        flag.hashCode;
  }
}

/// Generate emoji flag from country code
String emojiFlag(String countryCode) {
  if (countryCode.length != 2) return '🏳️';
  
  return countryCode
      .toUpperCase()
      .runes
      .map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41)))
      .join();
}
