import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/home_feed_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/home_feed_entity.g.dart';

@JsonSerializable()
class HomeFeedEntity {
  /// 幸运礼包 （需登录）
  HomeFeedLuckyGift? luckyGift;

  /// 热门活动 （需登录）
  @JSONField(name: "popular")
  List<HomeFeedPopular> popularList = [];

  /// 公告弹窗 (需登录)
  @JSONField(name: "notice")
  List<HomeFeedNotice> noticeList = [];

  /// 跑马灯（文字轮播）
  @JSONField(name: "marquee")
  List<HomeFeedMarquee> marqueeList = [];

  /// 欢迎礼包 （未登录）
  @JSONField(name: "welcome")
  List<HomeFeedWelcome> welcomeList = [];

  HomeFeedEntity();

  factory HomeFeedEntity.fromJson(Map<String, dynamic> json) => $HomeFeedEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedLuckyGift {
  double receiveMoney = 0;
  double bonusMoney = 0;
  int totalReceiveUser = 0;
  double totalReceiveMoney = 0;
  double remainingAmount = 0;
  double finishMoney = 0;
  int expiredTime = 0;
  int status = 0;
  @JSONField(name: "receiveUserListVOList")
  List<HomeFeedLuckyGiftReceiveUser> receiveUserList = [];

  HomeFeedLuckyGift();

  factory HomeFeedLuckyGift.fromJson(Map<String, dynamic> json) => $HomeFeedLuckyGiftFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedLuckyGiftToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedLuckyGiftReceiveUser {
  String userNo = '';
  int bonusMoney = 0;
  int finishedTime = 0;

  HomeFeedLuckyGiftReceiveUser();

  factory HomeFeedLuckyGiftReceiveUser.fromJson(Map<String, dynamic> json) =>
      $HomeFeedLuckyGiftReceiveUserFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedLuckyGiftReceiveUserToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedPopular {
  int id = 0;
  String currencyId = '';
  int noticeType = 0;
  int noticeClassify = 0;
  String noticeTitle = '';
  String noticeContent = '';
  int beginTime = 0;
  int endTime = 0;
  int receiveType = 0;
  int noticeSeq = 0;
  int noticeStatus = 0;
  int createTime = 0;
  int intoHistory = 0;
  int jumpStatus = 0;
  String jumpUrl = '';
  int gameBelong = 0;
  int venueId = 0;
  String platformCode = '';
  int gameId = 0;
  String tempId = '';

  HomeFeedPopular();

  factory HomeFeedPopular.fromJson(Map<String, dynamic> json) => $HomeFeedPopularFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedPopularToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedNotice {
  int id = 0;
  String currencyId = '';
  int noticeType = 0;
  int noticeClassify = 0;
  String noticeTitle = '';
  String noticeContent = '';
  int beginTime = 0;
  int endTime = 0;
  int receiveType = 0;
  int noticeSeq = 0;
  int noticeStatus = 0;
  int createTime = 0;
  int intoHistory = 0;
  int jumpStatus = 0;
  String jumpUrl = '';
  int gameBelong = 0;
  int venueId = 0;
  String platformCode = '';
  int gameId = 0;
  String tempId = '';

  HomeFeedNotice();

  factory HomeFeedNotice.fromJson(Map<String, dynamic> json) => $HomeFeedNoticeFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedNoticeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedMarquee {
  int id = 0;
  String currencyId = '';
  int noticeType = 0;
  int noticeClassify = 0;
  String noticeTitle = '';
  String noticeContent = '';
  int beginTime = 0;
  int endTime = 0;
  int receiveType = 0;
  int noticeSeq = 0;
  int noticeStatus = 0;
  int createTime = 0;
  int intoHistory = 0;
  int jumpStatus = 0;
  String jumpUrl = '';
  int gameBelong = 0;
  int venueId = 0;
  String platformCode = '';
  int gameId = 0;
  String tempId = '';

  HomeFeedMarquee();

  factory HomeFeedMarquee.fromJson(Map<String, dynamic> json) => $HomeFeedMarqueeFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedMarqueeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeFeedWelcome {
  int id = 0;
  String currencyId = '';
  int noticeType = 0;
  int noticeClassify = 0;
  String noticeTitle = '';
  String noticeContent = '';
  int beginTime = 0;
  int endTime = 0;
  int receiveType = 0;
  int noticeSeq = 0;
  int noticeStatus = 0;
  int createTime = 0;
  int intoHistory = 0;
  int jumpStatus = 0;
  String jumpUrl = '';
  int gameBelong = 0;
  int venueId = 0;
  String platformCode = '';
  int gameId = 0;
  String tempId = '';

  HomeFeedWelcome();

  factory HomeFeedWelcome.fromJson(Map<String, dynamic> json) => $HomeFeedWelcomeFromJson(json);

  Map<String, dynamic> toJson() => $HomeFeedWelcomeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
