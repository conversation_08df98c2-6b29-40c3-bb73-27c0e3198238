import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/filter_video_list_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/filter_video_list_entity.g.dart';

@JsonSerializable()
class FilterVideoListEntity {
  List<VideoHotMovies> records = [];
  int total = 0;
  int size = 0;
  int current = 0;

  FilterVideoListEntity();

  factory FilterVideoListEntity.fromJson(Map<String, dynamic> json) =>
      $FilterVideoListEntityFromJson(json);

  Map<String, dynamic> toJson() => $FilterVideoListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
