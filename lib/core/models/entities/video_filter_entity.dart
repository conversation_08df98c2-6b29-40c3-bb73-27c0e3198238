import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/video_filter_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/video_filter_entity.g.dart';
import 'package:easy_localization/easy_localization.dart';

@JsonSerializable()
class VideoFilterEntityList {
  List<VideoFilterEntity> list = [];

  VideoFilterEntityList();

  factory VideoFilterEntityList.fromJson(Map<String, dynamic> json) =>
      $VideoFilterEntityListFromJson(json);

  Map<String, dynamic> toJson() => $VideoFilterEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class VideoFilterEntity {
  late String videoCategory = '';
  late String videoCategoryId = '';

  VideoFilterEntity();

  /// 本地初始化全部分类
  static VideoFilterEntity initAllCategory() {
    return VideoFilterEntity()..videoCategory = 'all'.tr()..videoCategoryId = 'videoCategoryAll';
  }

  factory VideoFilterEntity.fromJson(Map<String, dynamic> json) =>
      $VideoFilterEntityFromJson(json);

  Map<String, dynamic> toJson() => $VideoFilterEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
