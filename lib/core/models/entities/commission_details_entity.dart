import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commission_details_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commission_details_entity.g.dart';

@JsonSerializable()
class CommissionDetailsEntity {
  List<CommissionDetailsRecords>? records;
  int? total;
  int? size;
  int? current;
  List<CommissionDetailsOrders>? orders;
  CommissionDetailsOptimizeCountSql? optimizeCountSql;
  CommissionDetailsSearchCount? searchCount;
  bool? optimizeJoinOfCountSql;
  int? maxLimit;
  String? countId;
  int? pages;

  CommissionDetailsEntity();

  factory CommissionDetailsEntity.fromJson(Map<String, dynamic> json) =>
      $CommissionDetailsEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommissionDetailsEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionDetailsRecords {
  int belongDate = 0;
  String subUserNo = "";
  double amount = 0;
  double commissionAmount = 0;

  CommissionDetailsRecords();

  factory CommissionDetailsRecords.fromJson(Map<String, dynamic> json) =>
      $CommissionDetailsRecordsFromJson(json);

  Map<String, dynamic> toJson() => $CommissionDetailsRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionDetailsOrders {
  String? column;
  bool? asc;

  CommissionDetailsOrders();

  factory CommissionDetailsOrders.fromJson(Map<String, dynamic> json) =>
      $CommissionDetailsOrdersFromJson(json);

  Map<String, dynamic> toJson() => $CommissionDetailsOrdersToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionDetailsOptimizeCountSql {
  CommissionDetailsOptimizeCountSql();

  factory CommissionDetailsOptimizeCountSql.fromJson(
          Map<String, dynamic> json) =>
      $CommissionDetailsOptimizeCountSqlFromJson(json);

  Map<String, dynamic> toJson() =>
      $CommissionDetailsOptimizeCountSqlToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionDetailsSearchCount {
  CommissionDetailsSearchCount();

  factory CommissionDetailsSearchCount.fromJson(Map<String, dynamic> json) =>
      $CommissionDetailsSearchCountFromJson(json);

  Map<String, dynamic> toJson() => $CommissionDetailsSearchCountToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
