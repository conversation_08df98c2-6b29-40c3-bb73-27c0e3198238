import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/country_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/country_entity.g.dart';

@JsonSerializable()
class CountryEntity {
	List<CountryList>? list = [];

	CountryEntity();

	factory CountryEntity.fromJson(Map<String, dynamic> json) => $CountryEntityFromJson(json);

	Map<String, dynamic> toJson() => $CountryEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CountryList {
	String? areaName = '';
	String? areaCode = '';
	String? areaIcon = '';

	CountryList();

	factory CountryList.fromJson(Map<String, dynamic> json) => $CountryListFromJson(json);

	Map<String, dynamic> toJson() => $CountryListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}