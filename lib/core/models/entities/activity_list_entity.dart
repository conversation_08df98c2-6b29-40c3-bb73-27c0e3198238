import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/activity_list_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class ActivityListEntity {
	late List<ActivityRecords> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late int pages = 0;

	ActivityListEntity();

	factory ActivityListEntity.fromJson(Map<String, dynamic> json) => $ActivityListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ActivityListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ActivityRecords {
	late int id = 0;
	late String activeName = '';
	late String beginTime = '';
	late String endTime = '';
	late int recordType = 0;
	late String bannerImage = '';
	late String detailImage = '';
	late int isTop = 0;
	late int activeStatus = 0;
	late int actTypeKey = 0;
	ActivityRecords();

	factory ActivityRecords.fromJson(Map<String, dynamic> json) => $ActivityRecordsFromJson(json);

	Map<String, dynamic> toJson() => $ActivityRecordsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class ActivityTaskListEntity {
	late List<ActivityTask> list = [];

	ActivityTaskListEntity();

	factory ActivityTaskListEntity.fromJson(Map<String, dynamic> json) => $ActivityTaskListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ActivityTaskListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ActivityTask {
	late int id = 0;
	late String title = '';
	late double sumAmount = 0;
	late int finishAmount = 0;
	late double receiveAmount;
	late String beginTime = '';
	late String endTime = '';
	late int receiveStatus = 0;
  /// 主类型1  
  /// ( 1-绑定手机号 2-绑定银行卡 3-VIP升级 4-彩票下注 5-体育下注 6-真人下注 7-电子下注 8-棋牌下注 9-捕鱼下注)   
  /// 主类型2  
  /// (10-视讯 11-捕鱼 12-棋牌 13-电子 14-彩票 15-体育)  
	late int subReceiveType = 0;
	late int subStatus = 0;

	bool isProcess = false;

	ActivityTask();

	factory ActivityTask.fromJson(Map<String, dynamic> json) => $ActivityTaskFromJson(json);

	Map<String, dynamic> toJson() => $ActivityTaskToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

	ActivityTask clone() {
		ActivityTask task = ActivityTask()
			..id = id
			..title = title
			..sumAmount = sumAmount
			..finishAmount = finishAmount
			..receiveAmount = receiveAmount
			..beginTime = beginTime
			..endTime = endTime
			..receiveStatus = receiveStatus
			..subReceiveType = subReceiveType
			..subStatus = subStatus
			..isProcess = isProcess;
		return task;
	}
}