import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/video_hot_tag_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/video_hot_tag_entity.g.dart';

@JsonSerializable()
class VideoHotTagEntity {
  List<String>? areaCategory = [];
  List<String>? videoYear = [];
  List<VideoHotTagMoviesCategory>? moviesCategory = [];

  VideoHotTagEntity();

  factory VideoHotTagEntity.fromJson(Map<String, dynamic> json) =>
      $VideoHotTagEntityFromJson(json);

  Map<String, dynamic> toJson() => $VideoHotTagEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class VideoHotTagMoviesCategory {
  @JSONField(name: 'dict_key')
  String dictKey = '';
  @JSONField(name: 'dict_value')
  String dictValue = '';

  VideoHotTagMoviesCategory();

  factory VideoHotTagMoviesCategory.fromJson(Map<String, dynamic> json) =>
      $VideoHotTagMoviesCategoryFromJson(json);

  Map<String, dynamic> toJson() => $VideoHotTagMoviesCategoryToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
