import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/team_members_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/team_members_entity.g.dart';

@JsonSerializable()
class TeamMembersEntity {
  List<TeamMembersRecords>? records;
  int? total;
  int? size;
  int? current;
  List<TeamMembersOrders>? orders;
  TeamMembersOptimizeCountSql? optimizeCountSql;
  TeamMembersSearchCount? searchCount;
  bool? optimizeJoinOfCountSql;
  int? maxLimit;
  String? countId;
  int? pages;

  TeamMembersEntity();

  factory TeamMembersEntity.fromJson(Map<String, dynamic> json) =>
      $TeamMembersEntityFromJson(json);

  Map<String, dynamic> toJson() => $TeamMembersEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamMembersRecords {
  String? subUserNo;
  int? level;
  int? registerDate;

  TeamMembersRecords();

  factory TeamMembersRecords.fromJson(Map<String, dynamic> json) =>
      $TeamMembersRecordsFromJson(json);

  Map<String, dynamic> toJson() => $TeamMembersRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamMembersOrders {
  String? column;
  bool? asc;

  TeamMembersOrders();

  factory TeamMembersOrders.fromJson(Map<String, dynamic> json) =>
      $TeamMembersOrdersFromJson(json);

  Map<String, dynamic> toJson() => $TeamMembersOrdersToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamMembersOptimizeCountSql {
  TeamMembersOptimizeCountSql();

  factory TeamMembersOptimizeCountSql.fromJson(Map<String, dynamic> json) =>
      $TeamMembersOptimizeCountSqlFromJson(json);

  Map<String, dynamic> toJson() => $TeamMembersOptimizeCountSqlToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamMembersSearchCount {
  TeamMembersSearchCount();

  factory TeamMembersSearchCount.fromJson(Map<String, dynamic> json) =>
      $TeamMembersSearchCountFromJson(json);

  Map<String, dynamic> toJson() => $TeamMembersSearchCountToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
