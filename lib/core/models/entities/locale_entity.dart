import 'package:flutter/material.dart';

class LocaleEntity {
  final String name;
  final String languageCode;
  final String countryCode;
  final bool isSelected;

  const LocaleEntity({
    required this.name,
    required this.languageCode,
    required this.countryCode,
    this.isSelected = false,
  });

  Locale toLocale() => Locale(languageCode, countryCode);

  LocaleEntity copyWith({
    String? name,
    String? languageCode,
    String? countryCode,
    bool? isSelected,
  }) {
    return LocaleEntity(
      name: name ?? this.name,
      languageCode: languageCode ?? this.languageCode,
      countryCode: countryCode ?? this.countryCode,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
