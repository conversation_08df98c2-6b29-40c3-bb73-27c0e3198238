// filename: kotlin_like_extensions.dart
// Kotlin-style utility extensions for Dart
// 一套 Kotlin 风格的 Dart 语法糖扩展（含空安全与异步场景）

// ===============================
// 1) let
// ===============================
// Use-case: Safely unwrap and transform a value.
// 用途：空安全取值并转换，避免中间变量与 if/三元判断。
// final len = "hello".let((it) => it.length); // 5
// final len = (null as String?).let((it) => it.length); // null
extension LetNullable<T> on T? {
  R? let<R>(R Function(T it) op) => this == null ? null : op(this as T);
}

// ===============================
// 2) also
// ===============================
// Use-case: Do side-effects in a chain and return the original object.
// 用途：链式副作用（日志、埋点、调试），返回原对象继续链式调用。
// final result = [1, 2, 3]
//     .also((it) => print("before: $it"))
//     .where((e) => e > 1)
//     .toList()
//     .also((it) => print("after: $it"));
extension AlsoNullable<T> on T? {
  T? also(void Function(T it) op) {
    final self = this;
    if (self != null) op(self);
    return self;
  }
}

// ===============================
// 5) takeIf / takeUnless
// ===============================
// Use-case: Conditionally keep or discard the value.
// 用途：按条件保留/丢弃值；可与 ?. 链一起构建优雅的条件链。
// final value = 10.takeIf((it) => it > 5);  // 10
// final dropped = 10.takeIf((it) => it < 5); // null
extension TakeIfNullable<T> on T? {
  T? takeIf(bool Function(T it) predicate) {
    final self = this;
    return (self != null && predicate(self)) ? self : null;
  }
}
