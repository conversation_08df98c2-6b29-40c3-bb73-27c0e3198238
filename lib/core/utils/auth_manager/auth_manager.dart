import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:wd/core/utils/log_util.dart';

enum LoginProvider {
  google,
  facebook,
  apple,
}

class AuthManager {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static late final GoogleSignIn _googleSignIn;
  static bool _isInitialized = false;

  /// 初始化 GoogleSignIn 实例
  static void _initializeGoogleSignIn() {
    if (_isInitialized) return;

    if (kIsWeb) {
      // Web 平台需要配置 clientId
      _googleSignIn = GoogleSignIn(
        clientId:
            '945554583431-ikdne5v345683n3qkmrq8cc5gssrt00f.apps.googleusercontent.com', // Web client ID from Firebase config
      );
      LogD("🌐 Google Sign-In initialized for Web");
    } else {
      // 移动端使用默认配置
      _googleSignIn = GoogleSignIn();
      LogD("📱 Google Sign-In initialized for Mobile");
    }
    _isInitialized = true;
  }

  /// 统一登录入口
  static Future<String?> signIn(LoginProvider provider) async {
    _initializeGoogleSignIn();

    switch (provider) {
      case LoginProvider.google:
        return _signInWithGoogle();

      case LoginProvider.facebook:
        // 以后在这里写 Facebook 登录
        throw UnimplementedError("Facebook 登录未实现");

      case LoginProvider.apple:
        // 以后在这里写 Apple 登录
        throw UnimplementedError("Apple 登录未实现");

      default:
        throw Exception("不支持的登录方式");
    }
  }

  static Future<String?> _signInWithGoogle() async {
    try {
      LogD("🔐 开始 Google 登录流程 - 平台: ${kIsWeb ? 'Web' : 'Mobile'}");

      // Web 平台特殊处理
      if (kIsWeb) {
        return await _signInWithGoogleWeb();
      } else {
        return await _signInWithGoogleMobile();
      }
    } catch (e) {
      LogE("❌ Google 登录失败: $e");
      return null;
    }
  }

  /// Web 平台 Google 登录
  static Future<String?> _signInWithGoogleWeb() async {
    try {
      // 先尝试静默登录
      final currentUser = _googleSignIn.currentUser;
      if (currentUser != null) {
        LogD("🔄 发现已登录用户，尝试刷新令牌");
        final googleAuth = await currentUser.authentication;
        if (googleAuth.idToken != null) {
          return await _authenticateWithFirebase(googleAuth);
        }
      }

      // 执行登录流程
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        LogD("⚠️ 用户取消了 Google 登录");
        return null;
      }

      final googleAuth = await googleUser.authentication;
      return await _authenticateWithFirebase(googleAuth);
    } catch (e) {
      LogE("❌ Web Google 登录失败: $e");

      // Web 特定错误处理
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('popup_blocked') || errorString.contains('popup')) {
        throw Exception("请允许弹窗并重试");
      } else if (errorString.contains('access_denied') || errorString.contains('denied')) {
        throw Exception("用户拒绝了授权");
      } else if (errorString.contains('network') || errorString.contains('connection')) {
        throw Exception("网络连接失败，请检查网络");
      } else if (errorString.contains('configuration') || errorString.contains('client')) {
        throw Exception("Google登录配置错误");
      } else if (errorString.contains('timeout')) {
        throw Exception("登录超时，请重试");
      }

      // 重新抛出原始异常以便上层处理
      rethrow;
    }
  }

  /// 移动端 Google 登录
  static Future<String?> _signInWithGoogleMobile() async {
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        LogD("⚠️ 用户取消了 Google 登录");
        return null;
      }

      final googleAuth = await googleUser.authentication;
      return await _authenticateWithFirebase(googleAuth);
    } catch (e) {
      LogE("❌ Mobile Google 登录失败: $e");
      return null;
    }
  }

  /// 使用 Google 凭据进行 Firebase 身份验证
  static Future<String?> _authenticateWithFirebase(GoogleSignInAuthentication googleAuth) async {
    try {
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      final idToken = await userCredential.user?.getIdToken();

      if (idToken != null) {
        LogD("✅ Firebase 身份验证成功");
      }

      return idToken;
    } catch (e) {
      LogE("❌ Firebase 身份验证失败: $e");
      return null;
    }
  }

  /// 登出
  static Future<void> signOut() async {
    try {
      _initializeGoogleSignIn();
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
      LogD("✅ 用户已登出");
    } catch (e) {
      LogE("❌ 登出失败: $e");
    }
  }

  /// 检查当前登录状态
  static bool get isSignedIn {
    _initializeGoogleSignIn();
    return _googleSignIn.currentUser != null && _auth.currentUser != null;
  }
}
