import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:wd/core/utils/string_util.dart';

Future<String> getFingerprint() async {
  return _getOrGenerateDeviceId();
}

Future<String> _getOrGenerateDeviceId() async {
  const deviceIdKey = 'device_uuid';
  final prefs = await SharedPreferences.getInstance();

  String? deviceId = prefs.getString(deviceIdKey);
  if (deviceId != null) {
    return deviceId;
  }
  deviceId = await _generateDeviceId();
  await prefs.setString(deviceIdKey, deviceId);
  return deviceId;
}

Future<String> _generateDeviceId() async {
  final deviceInfo = DeviceInfoPlugin();
  String deviceIdentifier = StringUtil.generateRandomString(19);

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfo.androidInfo;
    deviceIdentifier = androidInfo.id;
  } else if (Platform.isIOS) {
    final iosInfo = await deviceInfo.iosInfo;
    final id = iosInfo.identifierForVendor;
    if (id != null) {
      deviceIdentifier = id;
    }
  }

  return const Uuid().v5(Namespace.url.value, deviceIdentifier);
}
