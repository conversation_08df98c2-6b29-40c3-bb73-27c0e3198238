import 'dart:async';
import 'package:flutter/widgets.dart';

/// 全局 APP 前后台状态监听服务
/// Global service for monitoring app foreground/background status
class AppLifecycleService with WidgetsBindingObserver {
  // 单例模式 Singleton pattern
  AppLifecycleService._internal();

  static final AppLifecycleService instance = AppLifecycleService._internal();

  /// 当前是否在后台（true = 后台, false = 前台）
  /// Whether the app is currently in the background (true = background, false = foreground)
  bool _inBackground = false;

  bool get inBackground => _inBackground;

  /// 状态变化流（true=后台，false=前台）
  /// Stream of status changes (true = background, false = foreground)
  final _controller = StreamController<bool>.broadcast();

  Stream<bool> get stream => _controller.stream;

  /// 可选回调：进入后台时触发
  /// Optional callback: triggered when entering background
  VoidCallback? onEnterBackground;

  /// 可选回调：回到前台时触发
  /// Optional callback: triggered when returning to foreground
  VoidCallback? onEnterForeground;

  /// 初始化监听（需在应用启动时调用一次）
  /// Initialize listener (should be called once at app startup)
  void init() {
    WidgetsBinding.instance.addObserver(this);
  }

  /// 释放资源（一般不需要手动调用）
  /// Dispose resources (usually not needed manually)
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.close();
  }

  /// Flutter 生命周期状态变化回调
  /// Flutter lifecycle state change callback
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final wasInBackground = _inBackground;

    switch (state) {
      case AppLifecycleState.resumed:
        // 回到前台 Returning to foreground
        _inBackground = false;
        if (wasInBackground != _inBackground) {
          _controller.add(_inBackground);
          onEnterForeground?.call();
        }
        break;
      case AppLifecycleState.paused:
        // 进入后台 Entering background
        _inBackground = true;
        if (wasInBackground != _inBackground) {
          _controller.add(_inBackground);
          onEnterBackground?.call();
        }
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // 保持当前状态 Keep current state
        break;
    }
  }
}
