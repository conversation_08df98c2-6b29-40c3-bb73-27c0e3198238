{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "FWD (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main.dart",
      "args": [
        "--flavor",
        "WD",
        "--dart-define",
        "CHANNEL=WD",
        "--dart-define",
        "DEBUG=true",
        "--web-renderer",
        "html"
      ],
      // "preLaunchTask": "swap-wd-assets"
    },
    {
      "name": "FWD (release)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main.dart",
      "args": [
        "--dart-define",
        "CHANNEL=WD",
        "--dart-define",
        "DEBUG=true",
        "--web-renderer",
        "html"
      ],
//      "preLaunchTask": "swap-wd-assets"
    },
  ]
}